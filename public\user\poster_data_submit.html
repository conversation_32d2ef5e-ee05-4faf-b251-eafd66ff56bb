<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报参数填写</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .config-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .form-container { background: #fff; padding: 30px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .parameter-group { margin-bottom: 20px; }
        .parameter-label { font-weight: bold; margin-bottom: 5px; }
        .required { color: #FF5722; }
        .btn-group { text-align: center; margin-top: 30px; }
        .preview-area { margin-top: 20px; padding: 20px; background: #f5f5f5; border-radius: 5px; }
        .step-indicator { margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>海报参数填写</h2>
            <p>请填写以下参数来生成您的个性化海报</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="layui-progress layui-progress-big" lay-showpercent="true">
                <div class="layui-progress-bar layui-bg-blue" lay-percent="33%"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span class="layui-badge layui-bg-blue">1</span> 填写参数 → 
                <span class="layui-badge">2</span> 预览确认 → 
                <span class="layui-badge">3</span> 生成海报
            </div>
        </div>

        <!-- 配置信息 -->
        <div class="config-info">
            <h3 id="configTitle">加载中...</h3>
            <p id="configDescription">正在加载配置信息...</p>
            <div class="layui-row">
                <div class="layui-col-md6">
                    <strong>模板ID:</strong> <span id="templateId">-</span>
                </div>
                <div class="layui-col-md6">
                    <strong>参数数量:</strong> <span id="parameterCount">-</span>
                </div>
            </div>
        </div>

        <!-- 参数填写表单 -->
        <div class="form-container">
            <form class="layui-form" id="parameterForm">
                <input type="hidden" id="configId" name="config_id">
                <input type="hidden" id="templateIdInput" name="template_id">
                
                <div id="parametersContainer">
                    <!-- 参数输入项将在这里动态生成 -->
                </div>

                <div class="btn-group">
                    <button type="button" class="layui-btn layui-btn-primary" onclick="saveDraft()">
                        <i class="layui-icon layui-icon-file"></i> 保存草稿
                    </button>
                    <button type="button" class="layui-btn layui-btn-normal" onclick="previewData()">
                        <i class="layui-icon layui-icon-eye"></i> 预览
                    </button>
                    <button type="submit" class="layui-btn" lay-submit lay-filter="dataSubmit">
                        <i class="layui-icon layui-icon-ok"></i> 提交生成
                    </button>
                </div>
            </form>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area" id="previewArea" style="display: none;">
            <h3>参数预览</h3>
            <div id="previewContent"></div>
            <div style="text-align: center; margin-top: 15px;">
                <button type="button" class="layui-btn layui-btn-primary" onclick="hidePreview()">关闭预览</button>
                <button type="button" class="layui-btn" onclick="confirmSubmit()">确认提交</button>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'element'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;

            // 从URL获取配置ID
            var urlParams = new URLSearchParams(window.location.search);
            var configId = urlParams.get('config_id') || 'TEST_CONFIG_123'; // 默认测试配置ID

            // 页面加载时获取配置信息
            loadConfigInfo(configId);

            // 表单提交
            form.on('submit(dataSubmit)', function(data){
                submitData(data.field, false); // false表示正式提交
                return false;
            });

            // 加载配置信息
            function loadConfigInfo(configId) {
                document.getElementById('configId').value = configId;
                
                fetch(`/api/poster/config-detail/${configId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            renderConfigInfo(data.data);
                            renderParameterForm(data.data.parameters);
                        } else {
                            layer.msg(data.msg || '加载配置失败', {icon: 2});
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        layer.msg('网络错误', {icon: 2});
                    });
            }

            // 渲染配置信息
            function renderConfigInfo(config) {
                document.getElementById('configTitle').textContent = config.config_name;
                document.getElementById('configDescription').textContent = config.config_description || '暂无描述';
                document.getElementById('templateId').textContent = config.template_id;
                document.getElementById('templateIdInput').value = config.template_id;
                
                try {
                    var parameters = typeof config.parameters === 'string' ? 
                        JSON.parse(config.parameters) : config.parameters;
                    document.getElementById('parameterCount').textContent = parameters.length;
                } catch(e) {
                    document.getElementById('parameterCount').textContent = '0';
                }
            }

            // 渲染参数表单
            function renderParameterForm(parametersData) {
                var container = document.getElementById('parametersContainer');
                container.innerHTML = '';

                try {
                    var parameters = typeof parametersData === 'string' ? 
                        JSON.parse(parametersData) : parametersData;
                    
                    if (!Array.isArray(parameters)) {
                        throw new Error('参数数据格式错误');
                    }

                    // 按显示顺序排序
                    parameters.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));

                    parameters.forEach(function(param, index) {
                        if (!param.isEnabled) return; // 跳过未启用的参数

                        var parameterHtml = createParameterInput(param, index);
                        container.insertAdjacentHTML('beforeend', parameterHtml);
                    });

                    form.render(); // 重新渲染表单
                } catch(e) {
                    console.error('解析参数数据失败:', e);
                    container.innerHTML = '<div class="layui-form-item"><div class="layui-form-mid layui-word-aux">参数配置错误，请联系管理员</div></div>';
                }
            }

            // 创建参数输入项
            function createParameterInput(param, index) {
                var requiredMark = param.isRequired ? '<span class="required">*</span>' : '';
                var requiredAttr = param.isRequired ? 'required lay-verify="required"' : '';
                var inputType = getInputType(param.parameterType);
                var placeholder = `请输入${param.parameterLabel}`;

                return `
                    <div class="parameter-group">
                        <div class="layui-form-item">
                            <label class="layui-form-label parameter-label">
                                ${param.parameterLabel} ${requiredMark}
                            </label>
                            <div class="layui-input-block">
                                ${createInputElement(param, inputType, requiredAttr, placeholder)}
                                ${param.description ? `<div class="layui-form-mid layui-word-aux">${param.description}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }

            // 获取输入类型
            function getInputType(parameterType) {
                switch(parameterType) {
                    case 'number': return 'number';
                    case 'email': return 'email';
                    case 'url': return 'url';
                    case 'password': return 'password';
                    case 'tel': return 'tel';
                    default: return 'text';
                }
            }

            // 创建输入元素
            function createInputElement(param, inputType, requiredAttr, placeholder) {
                switch(param.parameterType) {
                    case 'textarea':
                        return `<textarea name="${param.parameterName}" ${requiredAttr} placeholder="${placeholder}" class="layui-textarea"></textarea>`;
                    case 'select':
                        var options = param.options || [];
                        var optionsHtml = options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('');
                        return `<select name="${param.parameterName}" ${requiredAttr}><option value="">请选择</option>${optionsHtml}</select>`;
                    case 'radio':
                        var radios = param.options || [];
                        return radios.map((opt, i) => 
                            `<input type="radio" name="${param.parameterName}" value="${opt.value}" title="${opt.label}" ${i === 0 ? 'checked' : ''}>`
                        ).join('');
                    case 'checkbox':
                        return `<input type="checkbox" name="${param.parameterName}" lay-skin="switch" lay-text="是|否">`;
                    default:
                        return `<input type="${inputType}" name="${param.parameterName}" ${requiredAttr} placeholder="${placeholder}" autocomplete="off" class="layui-input">`;
                }
            }

            // 保存草稿
            window.saveDraft = function() {
                var formData = new FormData(document.getElementById('parameterForm'));
                var parameterValues = {};
                
                for (let [key, value] of formData.entries()) {
                    if (key !== 'config_id' && key !== 'template_id') {
                        parameterValues[key] = value;
                    }
                }

                submitData({
                    config_id: document.getElementById('configId').value,
                    template_id: document.getElementById('templateIdInput').value,
                    parameter_values: parameterValues
                }, true); // true表示草稿
            };

            // 预览数据
            window.previewData = function() {
                var formData = new FormData(document.getElementById('parameterForm'));
                var parameterValues = {};
                
                for (let [key, value] of formData.entries()) {
                    if (key !== 'config_id' && key !== 'template_id') {
                        parameterValues[key] = value;
                    }
                }

                var previewHtml = '<table class="layui-table">';
                previewHtml += '<thead><tr><th>参数名</th><th>参数值</th></tr></thead><tbody>';
                
                Object.keys(parameterValues).forEach(function(key) {
                    previewHtml += `<tr><td>${key}</td><td>${parameterValues[key] || '<span style="color: #ccc;">未填写</span>'}</td></tr>`;
                });
                
                previewHtml += '</tbody></table>';
                
                document.getElementById('previewContent').innerHTML = previewHtml;
                document.getElementById('previewArea').style.display = 'block';
                
                // 滚动到预览区域
                document.getElementById('previewArea').scrollIntoView({ behavior: 'smooth' });
            };

            // 隐藏预览
            window.hidePreview = function() {
                document.getElementById('previewArea').style.display = 'none';
            };

            // 确认提交
            window.confirmSubmit = function() {
                var formData = new FormData(document.getElementById('parameterForm'));
                var parameterValues = {};
                
                for (let [key, value] of formData.entries()) {
                    if (key !== 'config_id' && key !== 'template_id') {
                        parameterValues[key] = value;
                    }
                }

                submitData({
                    config_id: document.getElementById('configId').value,
                    template_id: document.getElementById('templateIdInput').value,
                    parameter_values: parameterValues
                }, false); // false表示正式提交
            };

            // 提交数据
            function submitData(data, isDraft) {
                var submitData = {
                    config_id: data.config_id,
                    template_id: data.template_id,
                    parameter_values: data.parameter_values,
                    is_draft: isDraft ? 1 : 0
                };

                var loadingIndex = layer.load(2, {content: isDraft ? '保存中...' : '提交中...'});

                fetch('/api/poster/submit-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(submitData)
                })
                .then(response => response.json())
                .then(data => {
                    layer.close(loadingIndex);
                    
                    if (data.code === 200) {
                        if (isDraft) {
                            layer.msg('草稿保存成功', {icon: 1});
                        } else {
                            layer.msg('提交成功！正在跳转...', {icon: 1, time: 2000}, function(){
                                // 跳转到结果页面
                                window.location.href = `/user/poster_result.html?data_id=${data.data.data_id}`;
                            });
                        }
                    } else {
                        layer.msg(data.msg || (isDraft ? '保存失败' : '提交失败'), {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadingIndex);
                    console.error('Error:', error);
                    layer.msg('网络错误', {icon: 2});
                });
            }

            // 更新进度条
            function updateProgress(percent, step) {
                element.progress('demo', percent + '%');
                
                // 更新步骤指示器
                var badges = document.querySelectorAll('.step-indicator .layui-badge');
                badges.forEach(function(badge, index) {
                    if (index < step) {
                        badge.className = 'layui-badge layui-bg-blue';
                    } else if (index === step - 1) {
                        badge.className = 'layui-badge layui-bg-orange';
                    } else {
                        badge.className = 'layui-badge';
                    }
                });
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有保存的草稿
            var configId = new URLSearchParams(window.location.search).get('config_id');
            if (configId) {
                checkDraft(configId);
            }
        });

        // 检查草稿
        function checkDraft(configId) {
            // 这里可以添加检查草稿的逻辑
            // 如果有草稿，询问用户是否加载
        }
    </script>
</body>
</html>
