# 后台管理系统集成说明

## 📋 概述

本文档说明如何将动态参数模板系统集成到现有的 likeshop 后台管理系统中，使管理员可以直接在后台管理界面中管理海报模板配置和用户数据。

## 🔧 集成步骤

### 第一步：数据库初始化

1. **执行数据库迁移脚本**：
```bash
# 创建海报相关数据表
mysql -u username -p database_name < database/migrations/create_poster_template_tables.sql

# 添加后台管理菜单
mysql -u username -p database_name < database/migrations/add_poster_menu.sql
```

2. **初始化测试数据**：
```bash
php tests/init_test_database.php
```

### 第二步：文件部署

确保以下文件已正确部署：

#### 控制器文件
- `application/admin/controller/PosterManage.php` - 海报管理控制器

#### 逻辑层文件
- `application/admin/logic/PosterManageLogic.php` - 海报管理业务逻辑

#### 视图文件
- `application/admin/view/poster_manage/config_list.html` - 配置列表页面
- `application/admin/view/poster_manage/add_config.html` - 添加配置页面
- `application/admin/view/poster_manage/data_list.html` - 数据管理页面

#### 模型文件（如果不存在）
- `application/common/model/PosterTemplateConfig.php`
- `application/common/model/PosterUserData.php`
- `application/common/model/PosterGenerationRecord.php`

### 第三步：路由配置

路由配置已添加到 `route/route.php` 文件中，包含以下路由：

```php
// 配置管理
admin/poster_manage/config_list      - 配置列表
admin/poster_manage/add_config       - 添加配置
admin/poster_manage/edit_config      - 编辑配置
admin/poster_manage/del_config       - 删除配置
admin/poster_manage/config_detail    - 配置详情

// 数据管理
admin/poster_manage/data_list        - 数据列表
admin/poster_manage/data_detail      - 数据详情
admin/poster_manage/del_data         - 删除数据
admin/poster_manage/export_data      - 导出数据

// 统计分析
admin/poster_manage/statistics       - 统计分析
```

### 第四步：权限配置

1. **检查菜单是否正确添加**：
```sql
SELECT id, pid, name, uri, icon FROM ls_dev_auth 
WHERE name LIKE '%海报%' OR uri LIKE '%poster_manage%'
ORDER BY pid, sort DESC;
```

2. **为管理员角色分配权限**：
如果需要为特定角色分配权限，请在角色权限表中添加相应记录。

## 🌐 访问方式

### 管理员登录后台

1. **访问后台登录页面**：
   ```
   http://your-domain.com/admin/account/login
   ```

2. **登录后进入后台主页**：
   ```
   http://your-domain.com/admin/index/index
   ```

3. **在左侧菜单中找到"海报管理"**：
   - 配置管理 - 管理模板参数配置
   - 数据管理 - 查看用户提交的数据
   - 统计分析 - 查看系统统计信息

### 直接访问功能页面

如果已经登录，也可以直接访问具体功能页面：

- **配置管理**：`http://your-domain.com/admin/poster_manage/config_list`
- **数据管理**：`http://your-domain.com/admin/poster_manage/data_list`
- **统计分析**：`http://your-domain.com/admin/poster_manage/statistics`

### 功能模块说明

#### 1. 配置管理模块
- **功能**：创建、编辑、删除模板参数配置
- **访问路径**：海报管理 → 配置管理
- **主要操作**：
  - 添加新配置
  - 编辑现有配置
  - 启用/禁用配置
  - 复制配置
  - 解析模板参数

#### 2. 数据管理模块
- **功能**：查看和管理用户提交的参数数据
- **访问路径**：海报管理 → 数据管理
- **主要操作**：
  - 查看数据列表
  - 查看数据详情
  - 删除数据
  - 批量删除
  - 导出数据

#### 3. 统计分析模块
- **功能**：查看系统使用统计
- **访问路径**：海报管理 → 统计分析
- **统计内容**：
  - 配置数量统计
  - 用户数据统计
  - 生成记录统计
  - 今日新增统计

## 🎯 使用流程

### 管理员操作流程

1. **创建模板配置**：
   - 进入"配置管理"
   - 点击"添加配置"
   - 输入模板ID（可使用"解析模板"功能自动获取参数）
   - 配置参数信息
   - 保存配置

2. **管理用户数据**：
   - 进入"数据管理"
   - 查看用户提交的数据
   - 可以查看详情、删除或导出数据

3. **查看统计信息**：
   - 进入"统计分析"
   - 查看系统使用情况

### 用户使用流程

用户可以通过以下方式使用系统：

1. **直接访问用户页面**：
   ```
   http://your-domain.com/user/poster_data_submit.html?config_id=配置ID
   ```

2. **通过API接口**：
   使用外部API接口集成到其他系统中

## 🔧 配置说明

### 模板解析功能

在添加配置时，可以使用"解析模板"功能：
1. 输入模板ID
2. 点击"解析模板"按钮
3. 系统会自动获取模板的可用参数
4. 自动填充参数配置表单

### 参数类型说明

支持的参数类型：
- **text** - 文本输入框
- **number** - 数字输入框
- **email** - 邮箱输入框
- **url** - 网址输入框
- **textarea** - 多行文本框

### 权限控制

系统支持基于角色的权限控制：
- 不同角色可以分配不同的操作权限
- 支持菜单级别和操作级别的权限控制

## 🚨 注意事项

### 1. 数据库兼容性
- 确保数据库支持JSON字段类型
- MySQL版本建议5.7+

### 2. PHP版本要求
- PHP 7.0+ ~ PHP 7.x
- 不支持PHP 8.0+

### 3. 权限配置
- 新添加的菜单默认只有超级管理员可见
- 需要为其他角色手动分配权限

### 4. 模板服务配置
- 确保迅排设计服务配置正确
- 检查API密钥和服务地址

## 🔍 故障排查

### 常见问题

1. **菜单不显示**：
   - 检查数据库中菜单是否正确添加
   - 检查当前用户角色是否有权限
   - 清除缓存后重新登录

2. **页面404错误**：
   - 检查路由配置是否正确
   - 检查控制器文件是否存在
   - 检查文件权限

3. **数据库错误**：
   - 检查数据表是否正确创建
   - 检查数据库连接配置
   - 检查字段类型是否匹配

### 调试方法

1. **开启调试模式**：
```php
// config/app.php
'app_debug' => true,
'app_trace' => true,
```

2. **查看日志**：
```bash
tail -f runtime/log/error.log
```

3. **测试API接口**：
```bash
curl -X GET "http://your-domain.com/admin/poster_manage/config_list"
```

## 📞 技术支持

如有问题，请参考：
- API接口文档：`docs/API接口详细文档.md`
- 功能使用指南：`docs/动态参数模板功能使用指南.md`
- 测试报告：`docs/API功能测试报告.md`

---

**文档版本**: v1.0  
**最后更新**: 2025年8月17日  
**适用系统**: likeshop v3.0.3.20231204
