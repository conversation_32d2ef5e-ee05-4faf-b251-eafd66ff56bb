<?php

namespace app\common\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 海报用户参数数据模型
 * Class PosterUserData
 * @package app\common\model
 */
class PosterUserData extends Model
{
    use SoftDelete;

    protected $name = 'poster_user_data';
    protected $pk = 'id';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

    // JSON字段
    protected $json = ['parameter_values'];
    protected $jsonAssoc = true;

    /**
     * 草稿状态常量
     */
    const DRAFT_YES = 1; // 草稿
    const DRAFT_NO = 0;  // 正式

    /**
     * 获取草稿状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsDraftTextAttr($value, $data)
    {
        $status = [
            self::DRAFT_YES => '草稿',
            self::DRAFT_NO => '正式',
        ];
        return $status[$data['is_draft']] ?? '未知';
    }

    /**
     * 参数值获取器 - 确保返回数组格式
     * @param $value
     * @return array
     */
    public function getParameterValuesAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 参数值设置器 - 确保存储为JSON格式
     * @param $value
     * @return string
     */
    public function setParameterValuesAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 更新时间获取器
     * @param $value
     * @return string
     */
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 关联模板配置
     * @return \think\model\relation\BelongsTo
     */
    public function config()
    {
        return $this->belongsTo(PosterTemplateConfig::class, 'config_id', 'id');
    }

    /**
     * 关联生成记录
     * @return \think\model\relation\HasMany
     */
    public function generationRecords()
    {
        return $this->hasMany(PosterGenerationRecord::class, 'data_id', 'id');
    }

    /**
     * 获取用户数据列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::with(['config']);
        
        if (!empty($where['config_id'])) {
            $query->where('config_id', $where['config_id']);
        }
        
        if (!empty($where['template_id'])) {
            $query->where('template_id', $where['template_id']);
        }
        
        if (!empty($where['user_id'])) {
            $query->where('user_id', $where['user_id']);
        }
        
        if (!empty($where['session_id'])) {
            $query->where('session_id', $where['session_id']);
        }
        
        if (isset($where['is_draft'])) {
            $query->where('is_draft', $where['is_draft']);
        }

        $total = $query->count();
        $list = $query->order('create_time desc')
                     ->page($page, $limit)
                     ->select()
                     ->toArray();

        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 创建用户数据
     * @param array $data
     * @return PosterUserData|false
     */
    public static function createData($data)
    {
        $userData = new self();
        $userData->id = $data['id'] ?? self::generateId();
        $userData->config_id = $data['config_id'];
        $userData->template_id = $data['template_id'];
        $userData->user_id = $data['user_id'] ?? 0;
        $userData->session_id = $data['session_id'] ?? '';
        $userData->parameter_values = is_array($data['parameter_values']) ? json_encode($data['parameter_values']) : $data['parameter_values'];
        $userData->is_draft = $data['is_draft'] ?? self::DRAFT_YES;
        $userData->preview_url = $data['preview_url'] ?? '';
        $userData->generated_image_url = $data['generated_image_url'] ?? '';
        
        return $userData->save() ? $userData : false;
    }

    /**
     * 更新用户数据
     * @param string $id
     * @param array $data
     * @return bool
     */
    public static function updateUserData($id, $data)
    {
        $userData = self::find($id);
        if (!$userData) {
            return false;
        }

        if (isset($data['parameter_values'])) {
            $userData->parameter_values = is_array($data['parameter_values']) ? json_encode($data['parameter_values']) : $data['parameter_values'];
        }
        if (isset($data['is_draft'])) {
            $userData->is_draft = $data['is_draft'];
        }
        if (isset($data['preview_url'])) {
            $userData->preview_url = $data['preview_url'];
        }
        if (isset($data['generated_image_url'])) {
            $userData->generated_image_url = $data['generated_image_url'];
        }

        return $userData->save();
    }

    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'user_data_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 根据配置验证参数值
     * @param array $parameterValues
     * @param array $configParameters
     * @return array [bool $isValid, array $errors]
     */
    public static function validateParameterValues($parameterValues, $configParameters)
    {
        $errors = [];
        
        foreach ($configParameters as $param) {
            if (!$param['isEnabled']) {
                continue;
            }
            
            $paramName = $param['parameterName'];
            $value = $parameterValues[$paramName] ?? '';
            
            // 必填验证
            if ($param['isRequired'] && empty($value)) {
                $errors[$paramName] = $param['parameterLabel'] . '不能为空';
                continue;
            }
            
            // 长度验证
            if (!empty($value) && isset($param['validationRules'])) {
                $rules = $param['validationRules'];
                
                if (isset($rules['maxLength']) && mb_strlen($value) > $rules['maxLength']) {
                    $errors[$paramName] = $param['parameterLabel'] . '长度不能超过' . $rules['maxLength'] . '个字符';
                }
                
                if (isset($rules['minLength']) && mb_strlen($value) < $rules['minLength']) {
                    $errors[$paramName] = $param['parameterLabel'] . '长度不能少于' . $rules['minLength'] . '个字符';
                }
                
                // 正则验证
                if (isset($rules['pattern']) && !empty($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
                    $errors[$paramName] = $rules['errorMessage'] ?? $param['parameterLabel'] . '格式不正确';
                }
            }
        }
        
        return [empty($errors), $errors];
    }
}
