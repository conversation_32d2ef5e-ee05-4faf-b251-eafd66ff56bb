<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\logic;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;
use think\Db;

/**
 * 海报管理逻辑层
 * Class PosterManageLogic
 * @package app\admin\logic
 */
class PosterManageLogic
{
    protected static $error = '';

    /**
     * 获取错误信息
     * @return string
     */
    public static function getError()
    {
        return self::$error;
    }

    /**
     * 获取配置列表
     * @param array $get
     * @return array
     */
    public static function getConfigLists($get)
    {
        $where = [];
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;

        // 搜索条件
        if (!empty($get['template_id'])) {
            $where[] = ['template_id', '=', $get['template_id']];
        }
        if (!empty($get['status'])) {
            $where[] = ['status', '=', $get['status']];
        }
        if (!empty($get['keyword'])) {
            $where[] = ['config_name', 'like', '%' . $get['keyword'] . '%'];
        }

        $count = PosterTemplateConfig::where($where)->count();
        $lists = PosterTemplateConfig::where($where)
            ->order('create_time desc')
            ->page($page, $limit)
            ->select();

        // 处理数据
        foreach ($lists as &$item) {
            // 安全处理 parameters 字段
            try {
                if (is_array($item->parameters)) {
                    $parameters = $item->parameters;
                } elseif (is_string($item->parameters)) {
                    $parameters = json_decode($item->parameters, true);
                } else {
                    $parameters = [];
                }
            } catch (\Exception $e) {
                $parameters = [];
            }

            $item->parameter_count = is_array($parameters) ? count($parameters) : 0;

            // 安全处理时间戳
            $createTime = is_numeric($item->create_time) ? (int)$item->create_time : time();
            $updateTime = is_numeric($item->update_time) ? (int)$item->update_time : time();

            $item->create_time_text = date('Y-m-d H:i:s', $createTime);
            $item->update_time_text = date('Y-m-d H:i:s', $updateTime);
        }

        return [
            'count' => (int)$count,
            'lists' => $lists->toArray(), // 转换为数组
            'page_no' => (int)$page,
            'page_size' => (int)$limit
        ];
    }

    /**
     * 添加配置
     * @param array $post
     * @return bool
     */
    public static function addConfig($post)
    {
        try {
            $data = [
                'template_id' => $post['template_id'],
                'template_title' => $post['template_title'] ?? '',
                'config_name' => $post['config_name'],
                'config_description' => $post['config_description'] ?? '',
                'parameters' => is_array($post['parameters']) ? json_encode($post['parameters']) : $post['parameters'],
                'status' => $post['status'] ?? 1,
                'created_by' => 1, // 这里应该获取当前管理员ID
            ];

            $config = PosterTemplateConfig::createConfig($data);
            return $config ? true : false;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 编辑配置
     * @param array $post
     * @return bool
     */
    public static function editConfig($post)
    {
        try {
            $config = PosterTemplateConfig::find($post['id']);
            if (!$config) {
                self::$error = '配置不存在';
                return false;
            }

            $data = [
                'template_id' => $post['template_id'],
                'template_title' => $post['template_title'] ?? '',
                'config_name' => $post['config_name'],
                'config_description' => $post['config_description'] ?? '',
                'parameters' => is_array($post['parameters']) ? json_encode($post['parameters']) : $post['parameters'],
                'status' => $post['status'] ?? 1,
            ];

            return $config->updateConfig($data);
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 删除配置
     * @param string $id
     * @return bool
     */
    public static function delConfig($id)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                self::$error = '配置不存在';
                return false;
            }

            // 检查是否有关联的用户数据
            $dataCount = PosterUserData::where('config_id', $id)->count();
            if ($dataCount > 0) {
                self::$error = '该配置下还有用户数据，无法删除';
                return false;
            }

            return $config->delete();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取用户数据列表
     * @param array $get
     * @return array
     */
    public static function getDataLists($get)
    {
        $where = [];
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 20;

        // 搜索条件
        if (!empty($get['config_id'])) {
            $where[] = ['config_id', '=', $get['config_id']];
        }
        if (isset($get['is_draft']) && $get['is_draft'] !== '') {
            $where[] = ['is_draft', '=', $get['is_draft']];
        }
        if (!empty($get['user_id'])) {
            $where[] = ['user_id', '=', $get['user_id']];
        }

        $count = PosterUserData::where($where)->count();
        $lists = PosterUserData::where($where)
            ->order('create_time desc')
            ->page($page, $limit)
            ->select();

        // 处理数据
        foreach ($lists as &$item) {
            // 获取配置信息
            $config = PosterTemplateConfig::find($item->config_id);
            $item->config_name = $config ? $config->config_name : '未知配置';
            $item->template_title = $config ? $config->template_title : '';

            // 处理参数值
            try {
                if (is_array($item->parameter_values)) {
                    $parameterValues = $item->parameter_values;
                } elseif (is_string($item->parameter_values)) {
                    $parameterValues = json_decode($item->parameter_values, true);
                } else {
                    $parameterValues = [];
                }
            } catch (\Exception $e) {
                $parameterValues = [];
            }
            $item->parameter_count = is_array($parameterValues) ? count($parameterValues) : 0;

            // 安全处理时间戳
            $createTime = is_numeric($item->create_time) ? (int)$item->create_time : time();
            $updateTime = is_numeric($item->update_time) ? (int)$item->update_time : time();

            $item->create_time_text = date('Y-m-d H:i:s', $createTime);
            $item->update_time_text = date('Y-m-d H:i:s', $updateTime);
            $item->status_text = $item->is_draft ? '草稿' : '已提交';
        }

        return [
            'count' => $count,
            'lists' => $lists,
            'page_no' => $page,
            'page_size' => $limit
        ];
    }

    /**
     * 删除用户数据
     * @param string $id
     * @return bool
     */
    public static function delData($id)
    {
        try {
            $data = PosterUserData::find($id);
            if (!$data) {
                self::$error = '数据不存在';
                return false;
            }

            // 删除相关的生成记录
            PosterGenerationRecord::where('data_id', $id)->delete();

            return $data->delete();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 批量删除用户数据
     * @param array $ids
     * @return bool
     */
    public static function batchDelData($ids)
    {
        try {
            // 删除相关的生成记录
            PosterGenerationRecord::where('data_id', 'in', $ids)->delete();
            
            // 删除用户数据
            return PosterUserData::where('id', 'in', $ids)->delete();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 导出数据
     * @param array $get
     * @return array|bool
     */
    public static function exportData($get)
    {
        try {
            $where = [];
            
            // 搜索条件
            if (!empty($get['config_id'])) {
                $where[] = ['config_id', '=', $get['config_id']];
            }
            if (isset($get['is_draft']) && $get['is_draft'] !== '') {
                $where[] = ['is_draft', '=', $get['is_draft']];
            }

            $lists = PosterUserData::where($where)
                ->order('create_time desc')
                ->select();

            $exportData = [];
            foreach ($lists as $item) {
                $config = PosterTemplateConfig::find($item->config_id);
                try {
                    if (is_array($item->parameter_values)) {
                        $parameterValues = $item->parameter_values;
                    } elseif (is_string($item->parameter_values)) {
                        $parameterValues = json_decode($item->parameter_values, true);
                    } else {
                        $parameterValues = [];
                    }
                } catch (\Exception $e) {
                    $parameterValues = [];
                }
                
                $exportData[] = [
                    'id' => $item->id,
                    'config_name' => $config ? $config->config_name : '未知配置',
                    'template_id' => $item->template_id,
                    'user_id' => $item->user_id,
                    'parameter_values' => $parameterValues,
                    'is_draft' => $item->is_draft ? '草稿' : '已提交',
                    'create_time' => date('Y-m-d H:i:s', $item->create_time),
                    'update_time' => date('Y-m-d H:i:s', $item->update_time),
                ];
            }

            return $exportData;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 获取统计数据
     * @return array
     */
    public static function getStatistics()
    {
        $today = strtotime(date('Y-m-d'));
        $tomorrow = $today + 86400;

        return [
            'total_configs' => PosterTemplateConfig::count(),
            'active_configs' => PosterTemplateConfig::where('status', 1)->count(),
            'total_data' => PosterUserData::count(),
            'draft_data' => PosterUserData::where('is_draft', 1)->count(),
            'submitted_data' => PosterUserData::where('is_draft', 0)->count(),
            'today_data' => PosterUserData::where('create_time', '>=', $today)
                ->where('create_time', '<', $tomorrow)->count(),
            'total_generations' => PosterGenerationRecord::count(),
            'success_generations' => PosterGenerationRecord::where('status', 'completed')->count(),
        ];
    }

    /**
     * 解析模板
     * @param string $templateId
     * @return array|bool
     */
    public static function parseTemplate($templateId)
    {
        try {
            // 这里应该调用迅排设计服务的API来解析模板
            // 目前返回模拟数据
            return [
                'template_id' => $templateId,
                'template_title' => '模板标题',
                'available_parameters' => [
                    [
                        'id' => 'param-1',
                        'elementUuid' => 'element-uuid-1',
                        'parameterName' => 'user_name',
                        'parameterLabel' => '用户姓名',
                        'parameterType' => 'text',
                        'defaultValue' => '',
                        'description' => '用户的真实姓名'
                    ],
                    [
                        'id' => 'param-2',
                        'elementUuid' => 'element-uuid-2',
                        'parameterName' => 'job_title',
                        'parameterLabel' => '职位',
                        'parameterType' => 'text',
                        'defaultValue' => '',
                        'description' => '用户的职位信息'
                    ]
                ]
            ];
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 切换配置状态
     * @param string $id
     * @param int $status
     * @return bool
     */
    public static function switchStatus($id, $status)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                self::$error = '配置不存在';
                return false;
            }

            $config->status = $status;
            $config->update_time = time();
            return $config->save();
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 复制配置
     * @param string $id
     * @return array|bool
     */
    public static function copyConfig($id)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                self::$error = '配置不存在';
                return false;
            }

            $data = [
                'template_id' => $config->template_id,
                'template_title' => $config->template_title,
                'config_name' => $config->config_name . '_副本',
                'config_description' => $config->config_description,
                'parameters' => $config->parameters,
                'status' => 0, // 复制的配置默认为禁用状态
                'created_by' => 1, // 这里应该获取当前管理员ID
            ];

            $newConfig = PosterTemplateConfig::createConfig($data);
            return $newConfig ? ['id' => $newConfig->id] : false;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
}
