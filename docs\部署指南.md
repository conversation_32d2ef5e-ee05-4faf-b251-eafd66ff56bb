# 动态参数模板系统部署指南

## 📋 部署概述

本指南详细说明了如何在生产环境和测试环境中部署动态参数模板系统。

**系统要求**:
- PHP 7.0+ (推荐 7.2.9，不支持 PHP 8.0+)
- MySQL 5.7+
- Web服务器 (Apache/Nginx)
- Composer (依赖管理)

## 🔧 环境准备

### 1. 服务器环境检查

```bash
# 检查PHP版本
php -v

# 检查必需的PHP扩展
php -m | grep -E "(pdo|pdo_mysql|json|curl|mbstring|openssl)"

# 检查MySQL版本
mysql --version

# 检查Composer
composer --version
```

### 2. 必需的PHP扩展

确保以下PHP扩展已安装：
- `pdo` - 数据库抽象层
- `pdo_mysql` - MySQL数据库驱动
- `json` - JSON处理
- `curl` - HTTP客户端
- `mbstring` - 多字节字符串处理
- `openssl` - 加密支持
- `fileinfo` - 文件信息
- `gd` - 图像处理 (可选)

### 3. 目录权限设置

```bash
# 设置运行时目录权限
chmod -R 755 runtime/
chmod -R 755 public/

# 设置日志目录权限
chmod -R 755 runtime/log/

# 设置缓存目录权限
chmod -R 755 runtime/cache/
```

## 📦 部署步骤

### 第一步：下载和解压

```bash
# 克隆或下载项目代码
git clone <repository-url> likeshop-server
cd likeshop-server

# 或者解压上传的代码包
unzip likeshop-server.zip
cd likeshop-server
```

### 第二步：安装依赖

```bash
# 安装Composer依赖
composer install --no-dev --optimize-autoloader

# 如果在生产环境，使用以下命令
composer install --no-dev --optimize-autoloader --no-scripts
```

### 第三步：配置数据库

1. **创建数据库**:
```sql
CREATE DATABASE likeshop_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **配置数据库连接**:
编辑 `config/database.php`:
```php
return [
    'type'            => 'mysql',
    'hostname'        => 'localhost',
    'database'        => 'likeshop_db',
    'username'        => 'your_username',
    'password'        => 'your_password',
    'hostport'        => '3306',
    'charset'         => 'utf8mb4',
    'prefix'          => 'ls_',
    // 其他配置...
];
```

3. **初始化数据库**:
```bash
# 运行数据库初始化脚本
php tests/init_test_database.php
```

### 第四步：配置Web服务器

#### Apache配置

创建虚拟主机配置文件：
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/likeshop-server/public
    
    <Directory /path/to/likeshop-server/public>
        AllowOverride All
        Require all granted
        
        # 启用URL重写
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^(.*)$ index.php/$1 [QSA,PT,L]
    </Directory>
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/likeshop_error.log
    CustomLog ${APACHE_LOG_DIR}/likeshop_access.log combined
</VirtualHost>
```

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/likeshop-server/public;
    index index.php index.html;
    
    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    
    # 主要位置配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全：隐藏敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ /(config|runtime|tests|docs)/.*$ {
        deny all;
    }
}
```

### 第五步：配置应用

1. **应用配置** (`config/app.php`):
```php
return [
    'app_debug' => false,  // 生产环境关闭调试
    'app_trace' => false,  // 生产环境关闭追踪
    'default_timezone' => 'Asia/Shanghai',
    // 其他配置...
];
```

2. **海报配置** (`config/poster.php`):
```php
return [
    'xunpai_service' => [
        'base_url' => 'http://your-xunpai-service.com',
        'api_key' => 'your_xunpai_api_key',
        'timeout' => 30,
    ],
    // 其他配置...
];
```

### 第六步：设置API密钥

```bash
# 运行脚本创建默认API密钥
php -r "
require_once 'thinkphp/base.php';
define('APP_PATH', __DIR__ . '/application/');
define('ROOT_PATH', __DIR__ . '/');
\$app = new \think\App();
\$app->initialize();

\think\Db::table('ls_poster_api_keys')->insert([
    'api_key' => 'your_production_api_key_here',
    'key_name' => '生产环境API密钥',
    'permissions' => json_encode(['read', 'write']),
    'is_active' => 1,
    'create_time' => time(),
    'update_time' => time()
]);

echo 'API密钥创建成功\n';
"
```

### 第七步：验证部署

```bash
# 运行快速测试
php tests/quick_functional_test.php

# 运行完整测试
php tests/final_test_report.php
```

## 🔒 安全配置

### 1. 文件权限安全

```bash
# 设置安全的文件权限
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;

# 运行时目录需要写权限
chmod -R 755 runtime/
chmod -R 755 public/uploads/ # 如果有上传目录
```

### 2. 隐藏敏感目录

在Web服务器配置中添加：
```apache
# Apache
<Directory "/path/to/likeshop-server/config">
    Require all denied
</Directory>

<Directory "/path/to/likeshop-server/runtime">
    Require all denied
</Directory>
```

```nginx
# Nginx
location ~ /(config|runtime|tests|docs|vendor)/.*$ {
    deny all;
    return 404;
}
```

### 3. 环境变量配置

创建 `.env` 文件（如果支持）：
```env
APP_DEBUG=false
DB_HOST=localhost
DB_NAME=likeshop_db
DB_USER=your_username
DB_PASS=your_password
API_KEY=your_secure_api_key
```

## 📊 性能优化

### 1. PHP优化

在 `php.ini` 中设置：
```ini
# 内存限制
memory_limit = 256M

# 执行时间
max_execution_time = 60

# 文件上传
upload_max_filesize = 10M
post_max_size = 10M

# OPcache启用
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

### 2. 数据库优化

```sql
-- 为常用查询添加索引
ALTER TABLE ls_poster_template_configs ADD INDEX idx_template_status (template_id, status);
ALTER TABLE ls_poster_user_data ADD INDEX idx_config_user (config_id, user_id);
ALTER TABLE ls_poster_generation_records ADD INDEX idx_data_status (data_id, status);
```

### 3. 缓存配置

编辑 `config/cache.php`:
```php
return [
    'default' => 'redis',  // 使用Redis缓存
    'stores' => [
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 0,
            'timeout' => 0,
            'expire' => 3600,
        ],
    ],
];
```

## 🔍 监控和日志

### 1. 日志配置

编辑 `config/log.php`:
```php
return [
    'type' => 'File',
    'path' => LOG_PATH,
    'level' => ['error', 'warning'],  // 生产环境只记录错误和警告
    'file_size' => 2097152,  // 2MB
    'time_format' => 'Y-m-d H:i:s',
];
```

### 2. 监控脚本

创建监控脚本 `monitor.sh`:
```bash
#!/bin/bash

# 检查服务状态
check_service() {
    if curl -f -s "http://your-domain.com/api/external/health" > /dev/null; then
        echo "$(date): Service is running" >> /var/log/likeshop_monitor.log
    else
        echo "$(date): Service is down!" >> /var/log/likeshop_monitor.log
        # 发送告警邮件或短信
    fi
}

# 检查磁盘空间
check_disk() {
    USAGE=$(df /path/to/likeshop-server | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $USAGE -gt 80 ]; then
        echo "$(date): Disk usage is ${USAGE}%" >> /var/log/likeshop_monitor.log
    fi
}

check_service
check_disk
```

### 3. 定时任务

添加到 crontab:
```bash
# 每5分钟检查一次服务状态
*/5 * * * * /path/to/monitor.sh

# 每天清理过期日志
0 2 * * * find /path/to/likeshop-server/runtime/log -name "*.log" -mtime +7 -delete

# 每周运行系统测试
0 3 * * 0 cd /path/to/likeshop-server && php tests/final_test_report.php
```

## 🚀 部署检查清单

### 部署前检查
- [ ] 服务器环境满足要求
- [ ] 数据库已创建并配置
- [ ] 依赖包已安装
- [ ] 配置文件已正确设置
- [ ] 文件权限已设置

### 部署后检查
- [ ] 网站可以正常访问
- [ ] API接口响应正常
- [ ] 数据库连接正常
- [ ] 日志记录正常
- [ ] 测试脚本运行通过

### 安全检查
- [ ] 敏感目录已隐藏
- [ ] 调试模式已关闭
- [ ] API密钥已更换
- [ ] 文件权限安全
- [ ] 监控脚本已配置

## 🆘 故障排查

### 常见问题

1. **500错误**:
   - 检查PHP错误日志
   - 验证文件权限
   - 确认数据库连接

2. **API返回404**:
   - 检查URL重写规则
   - 验证路由配置
   - 确认控制器文件存在

3. **数据库连接失败**:
   - 检查数据库配置
   - 验证用户权限
   - 确认数据库服务运行

### 调试命令

```bash
# 检查PHP配置
php -i | grep -E "(error_log|display_errors)"

# 测试数据库连接
php -r "
require_once 'thinkphp/base.php';
define('APP_PATH', __DIR__ . '/application/');
\$app = new \think\App();
\$app->initialize();
try {
    \$result = \think\Db::query('SELECT 1');
    echo 'Database connection OK\n';
} catch (Exception \$e) {
    echo 'Database error: ' . \$e->getMessage() . '\n';
}
"

# 检查API健康状态
curl -H "Authorization: Bearer your_api_key" http://your-domain.com/api/external/health
```

## 📞 技术支持

如有部署问题，请参考：
- 项目文档：`docs/` 目录下的相关文档
- 测试工具：`tests/` 目录下的测试脚本
- 配置示例：`config/` 目录下的配置文件

---

**部署指南版本**: v1.0  
**最后更新**: 2025年8月17日
