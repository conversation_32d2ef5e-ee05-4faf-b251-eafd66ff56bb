{"info": {"name": "动态参数模板系统API测试", "description": "用于测试动态参数模板系统的所有API接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost", "description": "API基础URL"}, {"key": "api_key", "value": "poster_api_key_2025_secure_token_12345", "description": "API密钥"}, {"key": "config_id", "value": "", "description": "测试配置ID"}, {"key": "data_id", "value": "", "description": "测试数据ID"}], "item": [{"name": "外部API测试", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}"}], "url": {"raw": "{{base_url}}/api/external/health", "host": ["{{base_url}}"], "path": ["api", "external", "health"]}}, "response": []}, {"name": "获取参数配置", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}"}], "url": {"raw": "{{base_url}}/api/external/parameter-config/{{config_id}}", "host": ["{{base_url}}"], "path": ["api", "external", "parameter-config", "{{config_id}}"]}}, "response": []}, {"name": "获取参数数据", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{api_key}}"}], "url": {"raw": "{{base_url}}/api/external/parameter-data/{{data_id}}", "host": ["{{base_url}}"], "path": ["api", "external", "parameter-data", "{{data_id}}"]}}, "response": []}]}, {"name": "内部API测试", "item": [{"name": "解析模板", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": \"2\"\n}"}, "url": {"raw": "{{base_url}}/api/poster/parse-template", "host": ["{{base_url}}"], "path": ["api", "poster", "parse-template"]}}, "response": []}, {"name": "创建配置", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": \"2\",\n  \"config_name\": \"Postman测试配置\",\n  \"config_description\": \"通过Postman创建的测试配置\",\n  \"selected_parameters\": [\"param-1\"],\n  \"parameter_labels\": {\n    \"param-1\": \"测试参数\"\n  },\n  \"parameter_required\": {\n    \"param-1\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/api/poster/create-config", "host": ["{{base_url}}"], "path": ["api", "poster", "create-config"]}}, "response": []}, {"name": "获取配置列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/poster/config-list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "poster", "config-list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "获取配置详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/poster/config-detail/{{config_id}}", "host": ["{{base_url}}"], "path": ["api", "poster", "config-detail", "{{config_id}}"]}}, "response": []}, {"name": "提交用户数据", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"config_id\": \"{{config_id}}\",\n  \"parameter_values\": {\n    \"test_param\": \"Postman测试参数值\"\n  },\n  \"is_draft\": 1\n}"}, "url": {"raw": "{{base_url}}/api/poster/submit-data", "host": ["{{base_url}}"], "path": ["api", "poster", "submit-data"]}}, "response": []}, {"name": "获取用户数据列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/poster/user-data-list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "poster", "user-data-list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "获取用户数据详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/poster/user-data-detail/{{data_id}}", "host": ["{{base_url}}"], "path": ["api", "poster", "user-data-detail", "{{data_id}}"]}}, "response": []}, {"name": "生成预览", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data_id\": \"{{data_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/poster/generate-preview", "host": ["{{base_url}}"], "path": ["api", "poster", "generate-preview"]}}, "response": []}, {"name": "生成图片", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data_id\": \"{{data_id}}\",\n  \"options\": {\n    \"width\": 800,\n    \"height\": 600,\n    \"type\": \"file\",\n    \"size\": 2,\n    \"quality\": 0.9\n  }\n}"}, "url": {"raw": "{{base_url}}/api/poster/generate-image", "host": ["{{base_url}}"], "path": ["api", "poster", "generate-image"]}}, "response": []}]}]}