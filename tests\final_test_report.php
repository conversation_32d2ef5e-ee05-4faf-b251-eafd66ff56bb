<?php
/**
 * 最终测试报告生成脚本
 * 运行所有测试并生成综合报告
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once __DIR__ . '/../thinkphp/base.php';

// 设置应用路径
define('APP_PATH', __DIR__ . '/../application/');
define('ROOT_PATH', __DIR__ . '/../');

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== 动态参数模板系统最终测试报告 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";
echo "PHP版本: " . PHP_VERSION . "\n\n";

class FinalTestReport
{
    private $testResults = [];
    private $totalTests = 0;
    private $passedTests = 0;
    private $failedTests = 0;
    
    public function runAllTests()
    {
        echo "开始执行综合测试...\n\n";
        
        // 1. 数据库初始化测试
        $this->runTest('数据库初始化', 'testDatabaseInit');
        
        // 2. 基础功能测试
        $this->runTest('基础功能测试', 'testBasicFunctions');
        
        // 3. 业务逻辑测试
        $this->runTest('业务逻辑测试', 'testBusinessLogic');
        
        // 4. 数据完整性测试
        $this->runTest('数据完整性测试', 'testDataIntegrity');
        
        // 5. 安全性测试
        $this->runTest('安全性测试', 'testSecurity');
        
        // 生成最终报告
        $this->generateFinalReport();
    }
    
    private function runTest($testName, $testMethod)
    {
        $this->totalTests++;
        echo "执行测试: {$testName}\n";
        echo str_repeat('-', 50) . "\n";
        
        $startTime = microtime(true);
        
        try {
            $result = $this->$testMethod();
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            if ($result) {
                echo "✅ 通过 ({$duration}ms)\n\n";
                $this->passedTests++;
                $status = 'PASS';
            } else {
                echo "❌ 失败 ({$duration}ms)\n\n";
                $this->failedTests++;
                $status = 'FAIL';
            }
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            echo "❌ 异常: " . $e->getMessage() . " ({$duration}ms)\n\n";
            $this->failedTests++;
            $status = 'ERROR';
        }
        
        $this->testResults[] = [
            'name' => $testName,
            'status' => $status,
            'duration' => $duration ?? 0,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    private function testDatabaseInit()
    {
        echo "  检查数据库连接...\n";
        $result = \think\Db::query('SELECT 1 as test');
        if (empty($result)) {
            echo "    数据库连接失败\n";
            return false;
        }
        echo "    ✓ 数据库连接正常\n";
        
        echo "  检查表结构...\n";
        $tables = ['ls_poster_template_configs', 'ls_poster_user_data', 'ls_poster_generation_records', 'ls_poster_api_keys'];
        foreach ($tables as $table) {
            $exists = \think\Db::query("SHOW TABLES LIKE '{$table}'");
            if (empty($exists)) {
                echo "    表 {$table} 不存在\n";
                return false;
            }
        }
        echo "    ✓ 所有必需表都存在\n";
        
        echo "  检查API密钥...\n";
        $apiKey = \think\Db::table('ls_poster_api_keys')->where('is_active', 1)->find();
        if (!$apiKey) {
            echo "    没有可用的API密钥\n";
            return false;
        }
        echo "    ✓ API密钥配置正常\n";
        
        return true;
    }
    
    private function testBasicFunctions()
    {
        echo "  测试模型ID生成...\n";
        $configId = \app\common\model\PosterTemplateConfig::generateId();
        $dataId = \app\common\model\PosterUserData::generateId();
        
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $configId) || !preg_match('/^[a-zA-Z0-9_]+$/', $dataId)) {
            echo "    ID格式不正确\n";
            return false;
        }
        echo "    ✓ ID生成正常\n";
        
        echo "  测试模型类加载...\n";
        try {
            $config = new \app\common\model\PosterTemplateConfig();
            $userData = new \app\common\model\PosterUserData();
            $record = new \app\common\model\PosterGenerationRecord();
            echo "    ✓ 模型类加载正常\n";
        } catch (Exception $e) {
            echo "    模型类加载失败: " . $e->getMessage() . "\n";
            return false;
        }
        
        echo "  测试控制器文件存在性...\n";
        $controllerFiles = [
            __DIR__ . '/../application/api/controller/External.php',
            __DIR__ . '/../application/api/controller/Poster.php'
        ];

        foreach ($controllerFiles as $file) {
            if (!file_exists($file)) {
                echo "    控制器文件不存在: " . basename($file) . "\n";
                return false;
            }
        }
        echo "    ✓ 控制器文件存在\n";
        
        return true;
    }
    
    private function testBusinessLogic()
    {
        echo "  测试配置创建...\n";
        $configData = [
            'template_id' => '2',
            'config_name' => '最终测试配置',
            'parameters' => [
                [
                    'id' => 'final-test-param',
                    'elementUuid' => 'final-test-uuid',
                    'parameterName' => 'message',
                    'parameterLabel' => '消息',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true,
                    'displayOrder' => 1,
                ]
            ],
        ];
        
        $config = \app\common\model\PosterTemplateConfig::createConfig($configData);
        if (!$config) {
            echo "    配置创建失败\n";
            return false;
        }
        echo "    ✓ 配置创建成功\n";
        
        echo "  测试用户数据提交...\n";
        $userData = [
            'config_id' => $config->id,
            'template_id' => '2',
            'user_id' => 1,
            'parameter_values' => ['message' => '最终测试消息'],
            'is_draft' => 1,
        ];
        
        $userDataModel = \app\common\model\PosterUserData::createData($userData);
        if (!$userDataModel) {
            echo "    用户数据创建失败\n";
            return false;
        }
        echo "    ✓ 用户数据创建成功\n";
        
        echo "  清理测试数据...\n";
        $userDataModel->delete();
        $config->delete();
        echo "    ✓ 测试数据清理完成\n";
        
        return true;
    }
    
    private function testDataIntegrity()
    {
        echo "  测试数据关联完整性...\n";
        
        // 创建配置
        $config = \app\common\model\PosterTemplateConfig::createConfig([
            'template_id' => '2',
            'config_name' => '数据完整性测试配置',
            'parameters' => [['id' => 'test', 'parameterName' => 'test', 'parameterLabel' => '测试']],
        ]);
        
        // 创建用户数据
        $userData = \app\common\model\PosterUserData::createData([
            'config_id' => $config->id,
            'template_id' => '2',
            'user_id' => 1,
            'parameter_values' => ['test' => '测试值'],
        ]);
        
        // 验证关联
        $foundData = \app\common\model\PosterUserData::where('config_id', $config->id)->find();
        if (!$foundData || $foundData->id !== $userData->id) {
            echo "    数据关联验证失败\n";
            return false;
        }
        echo "    ✓ 数据关联完整性正常\n";
        
        // 清理
        $userData->delete();
        $config->delete();
        
        return true;
    }
    
    private function testSecurity()
    {
        echo "  测试API密钥验证...\n";
        
        // 测试有效密钥
        $validKey = \think\Db::table('ls_poster_api_keys')
            ->where('is_active', 1)
            ->find();
        if (!$validKey) {
            echo "    没有有效的API密钥\n";
            return false;
        }
        echo "    ✓ 有效API密钥验证通过\n";
        
        // 测试无效密钥
        $invalidKey = \think\Db::table('ls_poster_api_keys')
            ->where('api_key', 'invalid_key_12345')
            ->where('is_active', 1)
            ->find();
        if ($invalidKey) {
            echo "    无效API密钥不应该存在\n";
            return false;
        }
        echo "    ✓ 无效API密钥验证正确\n";
        
        echo "  测试参数验证...\n";
        $parameters = [[
            'id' => 'test',
            'parameterName' => 'test',
            'parameterLabel' => '测试参数',
            'parameterType' => 'text',
            'isRequired' => true,
            'isEnabled' => true,
            'displayOrder' => 1
        ]];

        // 测试有效参数
        list($isValid, $errors) = \app\common\model\PosterUserData::validateParameterValues(['test' => '有效值'], $parameters);
        if (!$isValid) {
            echo "    有效参数验证失败\n";
            return false;
        }

        // 测试无效参数
        list($isValid, $errors) = \app\common\model\PosterUserData::validateParameterValues([], $parameters);
        if ($isValid) {
            echo "    无效参数应该验证失败\n";
            return false;
        }
        echo "    ✓ 参数验证机制正常\n";
        
        return true;
    }
    
    private function generateFinalReport()
    {
        echo str_repeat('=', 60) . "\n";
        echo "最终测试报告\n";
        echo str_repeat('=', 60) . "\n";
        
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        echo "测试概览:\n";
        echo "- 总测试数: {$this->totalTests}\n";
        echo "- 通过测试: {$this->passedTests}\n";
        echo "- 失败测试: {$this->failedTests}\n";
        echo "- 成功率: {$successRate}%\n\n";
        
        echo "详细结果:\n";
        foreach ($this->testResults as $result) {
            $status = $result['status'] === 'PASS' ? '✅' : '❌';
            echo "  {$status} {$result['name']} ({$result['duration']}ms)\n";
        }
        
        echo "\n系统状态评估:\n";
        if ($this->failedTests === 0) {
            echo "🎉 系统功能完全正常！\n";
            echo "\n建议后续操作:\n";
            echo "1. 部署到测试环境进行集成测试\n";
            echo "2. 开发前端管理界面\n";
            echo "3. 与迅排设计服务进行联调\n";
            echo "4. 进行性能和压力测试\n";
            echo "5. 准备生产环境部署\n";
        } else {
            echo "⚠️ 系统存在 {$this->failedTests} 个问题需要解决\n";
            echo "\n建议后续操作:\n";
            echo "1. 查看详细日志排查失败的测试\n";
            echo "2. 修复相关问题\n";
            echo "3. 重新运行测试验证修复效果\n";
            echo "4. 更新相关文档\n";
        }
        
        // 保存报告到文件
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'test_results' => $this->testResults,
            'system_status' => $this->failedTests === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION'
        ];
        
        $reportFile = __DIR__ . '/final_test_report_' . date('YmdHis') . '.json';
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "\n详细报告已保存到: {$reportFile}\n";
    }
}

// 运行最终测试
if (php_sapi_name() === 'cli') {
    $reporter = new FinalTestReport();
    $reporter->runAllTests();
} else {
    echo "请在命令行中运行此测试脚本\n";
}
