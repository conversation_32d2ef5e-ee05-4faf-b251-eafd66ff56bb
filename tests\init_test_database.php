<?php
/**
 * 测试数据库初始化脚本
 * 用于创建测试所需的表结构和基础数据
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once __DIR__ . '/../thinkphp/base.php';

// 设置应用路径
define('APP_PATH', __DIR__ . '/../application/');
define('ROOT_PATH', __DIR__ . '/../');

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== 测试数据库初始化 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

try {
    // 检查数据库连接
    echo "1. 检查数据库连接...\n";
    $result = \think\Db::query('SELECT 1 as test');
    if (empty($result)) {
        throw new Exception("数据库连接失败");
    }
    echo "✅ 数据库连接正常\n\n";
    
    // 创建表结构
    echo "2. 创建表结构...\n";
    createTables();
    echo "✅ 表结构创建完成\n\n";
    
    // 插入基础数据
    echo "3. 插入基础数据...\n";
    insertBasicData();
    echo "✅ 基础数据插入完成\n\n";
    
    // 验证表结构
    echo "4. 验证表结构...\n";
    verifyTables();
    echo "✅ 表结构验证通过\n\n";
    
    echo "🎉 数据库初始化完成！\n";
    
} catch (Exception $e) {
    echo "❌ 初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 创建表结构
 */
function createTables()
{
    $tables = [
        'ls_poster_template_configs' => "
            CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
                `id` varchar(50) NOT NULL COMMENT '配置ID',
                `template_id` varchar(50) NOT NULL COMMENT '模板ID',
                `template_title` varchar(255) DEFAULT NULL COMMENT '模板标题',
                `config_name` varchar(255) NOT NULL COMMENT '配置名称',
                `config_description` text COMMENT '配置描述',
                `parameters` json NOT NULL COMMENT '参数配置',
                `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
                `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_template_id` (`template_id`),
                KEY `idx_created_by` (`created_by`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海报模板配置表'
        ",
        
        'ls_poster_user_data' => "
            CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
                `id` varchar(50) NOT NULL COMMENT '数据ID',
                `config_id` varchar(50) NOT NULL COMMENT '配置ID',
                `template_id` varchar(50) NOT NULL COMMENT '模板ID',
                `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
                `parameter_values` json NOT NULL COMMENT '参数值',
                `is_draft` tinyint(1) DEFAULT 1 COMMENT '是否草稿',
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_config_id` (`config_id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_template_id` (`template_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参数数据表'
        ",
        
        'ls_poster_generation_records' => "
            CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
                `id` varchar(50) NOT NULL COMMENT '记录ID',
                `data_id` varchar(50) NOT NULL COMMENT '数据ID',
                `config_id` varchar(50) NOT NULL COMMENT '配置ID',
                `template_id` varchar(50) NOT NULL COMMENT '模板ID',
                `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
                `generation_type` varchar(20) NOT NULL COMMENT '生成类型(preview/final)',
                `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态',
                `result_data` json DEFAULT NULL COMMENT '生成结果',
                `error_message` text COMMENT '错误信息',
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_data_id` (`data_id`),
                KEY `idx_config_id` (`config_id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海报生成记录表'
        ",
        
        'ls_poster_api_keys' => "
            CREATE TABLE IF NOT EXISTS `ls_poster_api_keys` (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
                `key_name` varchar(255) NOT NULL COMMENT '密钥名称',
                `description` text COMMENT '描述',
                `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                UNIQUE KEY `uk_api_key` (`api_key`),
                KEY `idx_is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API密钥管理表'
        "
    ];
    
    foreach ($tables as $tableName => $sql) {
        echo "  创建表: {$tableName}...\n";
        \think\Db::execute($sql);
    }
}

/**
 * 插入基础数据
 */
function insertBasicData()
{
    // 插入默认API密钥
    echo "  插入默认API密钥...\n";
    $apiKeyExists = \think\Db::table('ls_poster_api_keys')
        ->where('api_key', 'poster_api_key_2025_secure_token_12345')
        ->find();
        
    if (!$apiKeyExists) {
        \think\Db::table('ls_poster_api_keys')->insert([
            'api_key' => 'poster_api_key_2025_secure_token_12345',
            'key_name' => '默认测试API密钥',
            'permissions' => json_encode(['read', 'write']),
            'is_active' => 1,
            'create_time' => time(),
            'update_time' => time()
        ]);
        echo "    ✓ 默认API密钥已插入\n";
    } else {
        echo "    ✓ 默认API密钥已存在\n";
    }
    
    // 可以在这里添加其他基础数据
    echo "  插入测试配置数据...\n";
    $testConfigExists = \think\Db::table('ls_poster_template_configs')
        ->where('config_name', '测试配置')
        ->find();

    if (!$testConfigExists) {
        $configId = 'TEST_CONFIG_' . strtoupper(uniqid());
        \think\Db::table('ls_poster_template_configs')->insert([
            'id' => $configId,
            'template_id' => '2',
            'template_title' => '测试模板',
            'config_name' => '测试配置',
            'config_description' => '用于测试的配置',
            'parameters' => json_encode([
                [
                    'id' => 'test-param',
                    'elementUuid' => 'test-uuid',
                    'parameterName' => 'test_name',
                    'parameterLabel' => '测试参数',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true,
                    'displayOrder' => 1,
                ]
            ]),
            'status' => 1, // 使用实际的字段名
            'created_by' => 1,
            'create_time' => time(),
            'update_time' => time()
        ]);
        echo "    ✓ 测试配置已插入\n";
    } else {
        echo "    ✓ 测试配置已存在\n";
    }
}

/**
 * 验证表结构
 */
function verifyTables()
{
    $requiredTables = [
        'ls_poster_template_configs',
        'ls_poster_user_data',
        'ls_poster_generation_records',
        'ls_poster_api_keys'
    ];
    
    foreach ($requiredTables as $table) {
        echo "  验证表: {$table}...\n";
        
        // 检查表是否存在
        $exists = \think\Db::query("SHOW TABLES LIKE '{$table}'");
        if (empty($exists)) {
            throw new Exception("表 {$table} 不存在");
        }
        
        // 检查表结构
        $columns = \think\Db::query("DESCRIBE {$table}");
        if (empty($columns)) {
            throw new Exception("表 {$table} 结构为空");
        }
        
        echo "    ✓ 表 {$table} 验证通过 (" . count($columns) . " 个字段)\n";
    }
    
    // 验证数据
    echo "  验证基础数据...\n";
    $apiKeyCount = \think\Db::table('ls_poster_api_keys')->count();
    $configCount = \think\Db::table('ls_poster_template_configs')->count();
    
    echo "    ✓ API密钥数量: {$apiKeyCount}\n";
    echo "    ✓ 配置数量: {$configCount}\n";
}

echo "\n提示: 现在可以运行测试脚本了\n";
echo "快速测试: php tests/quick_functional_test.php\n";
echo "完整测试: php tests/run_comprehensive_test.php\n";
