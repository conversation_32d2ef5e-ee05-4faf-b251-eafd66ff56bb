<?php
/**
 * 检查数据库表是否存在
 */

require_once __DIR__ . '/thinkphp/base.php';

use think\facade\Db;

echo "=== 数据库表检查 ===\n\n";

try {
    // 检查海报模板配置表
    $tableName = 'ls_poster_template_configs';
    
    echo "检查表: {$tableName}\n";
    
    // 检查表是否存在
    $sql = "SHOW TABLES LIKE '{$tableName}'";
    $result = Db::query($sql);
    
    if (empty($result)) {
        echo "❌ 表 {$tableName} 不存在\n";
        echo "需要创建表，SQL如下：\n\n";
        
        $createSql = "
CREATE TABLE `{$tableName}` (
  `id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` varchar(255) DEFAULT '' COMMENT '模板标题',
  `config_name` varchar(255) NOT NULL COMMENT '配置名称',
  `config_description` text COMMENT '配置描述',
  `parameters` json NOT NULL COMMENT '参数定义JSON',
  `created_by` int(11) DEFAULT 0 COMMENT '创建者ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报模板参数配置表';
";
        
        echo $createSql . "\n";
        
        // 尝试创建表
        echo "正在创建表...\n";
        Db::execute($createSql);
        echo "✅ 表创建成功\n";
        
    } else {
        echo "✅ 表 {$tableName} 存在\n";
        
        // 检查表结构
        $columns = Db::query("DESCRIBE {$tableName}");
        echo "表结构:\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        }
        
        // 检查数据数量
        $count = Db::table($tableName)->count();
        echo "数据数量: {$count}\n";
    }
    
    echo "\n";
    
    // 检查用户数据表
    $userDataTable = 'ls_poster_user_data';
    echo "检查表: {$userDataTable}\n";
    
    $result = Db::query("SHOW TABLES LIKE '{$userDataTable}'");
    if (empty($result)) {
        echo "❌ 表 {$userDataTable} 不存在\n";
        
        $createUserDataSql = "
CREATE TABLE `{$userDataTable}` (
  `id` varchar(32) NOT NULL COMMENT '数据ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `user_id` int(11) DEFAULT 0 COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT '' COMMENT '会话ID（匿名用户）',
  `parameter_values` json NOT NULL COMMENT '用户填写的参数值',
  `is_draft` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为草稿：1草稿 0正式',
  `preview_url` varchar(500) DEFAULT '' COMMENT '预览页面URL',
  `generated_image_url` varchar(500) DEFAULT '' COMMENT '生成的图片URL',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_is_draft` (`is_draft`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报用户参数数据表';
";
        
        echo "正在创建用户数据表...\n";
        Db::execute($createUserDataSql);
        echo "✅ 用户数据表创建成功\n";
        
    } else {
        echo "✅ 表 {$userDataTable} 存在\n";
        $count = Db::table($userDataTable)->count();
        echo "数据数量: {$count}\n";
    }
    
    echo "\n=== 检查完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 检查过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
