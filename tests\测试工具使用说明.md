# 测试工具使用说明

本目录包含动态参数模板系统的测试工具和脚本。

## 🛠️ 可用的测试工具

### 核心测试脚本
- `init_test_database.php` - 数据库初始化和基础数据创建
- `quick_functional_test.php` - 快速功能验证测试
- `final_test_report.php` - 综合测试报告生成（包含完整的API和功能测试）

### 测试套件脚本
- `run_test_suite.sh` - Linux/Mac测试套件
- `run_test_suite.bat` - Windows测试套件

### 其他工具
- `postman_collection.json` - Postman API测试集合
- `测试检查清单.md` - 手动测试检查清单

## 🚀 快速开始

### 1. 初始化测试环境
```bash
php init_test_database.php
```

### 2. 运行快速测试
```bash
php quick_functional_test.php
```

### 3. 生成完整测试报告
```bash
php final_test_report.php
```

### 4. 运行测试套件（推荐）
```bash
# Linux/Mac
bash run_test_suite.sh

# Windows
run_test_suite.bat

# 快速测试模式
bash run_test_suite.sh --quick
```

## 📊 测试结果

最新测试结果：
- ✅ 所有核心功能测试通过
- ✅ 数据库操作正常
- ✅ 业务逻辑验证通过
- ✅ 安全机制工作正常

详细测试报告请查看：`docs/API功能测试报告.md`

## 🔧 测试环境要求

- PHP 7.0+ (推荐 7.2.9)
- MySQL 5.7+
- 已配置的数据库连接
- ThinkPHP 5.1框架

## ⚠️ 注意事项

- 测试前请确保数据库连接正常
- 测试会创建和删除临时数据
- 建议在测试环境中运行
- 某些测试需要API密钥配置

## 📞 故障排查

如果测试失败，请检查：
1. 数据库连接配置
2. PHP版本兼容性
3. 必需的PHP扩展
4. 文件权限设置

更多信息请参考：`docs/动态参数模板功能使用指南.md`
