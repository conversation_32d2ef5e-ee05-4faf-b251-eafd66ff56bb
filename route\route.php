<?php

use think\facade\Config;
use think\facade\Route;

//手机h5页面路由
Route::rule('mobile/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('h5', 'is_open', 1);
    if (!$isOpen) {
        return '';
    }
    Config::set('app_trace', false);
    return view(app()->getRootPath() . 'public/mobile/index.html');
})->pattern(['any' => '\w+']);

//手机h5页面路由
Route::rule('pc/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('pc', 'is_open', 1);
    if (!$isOpen) {
        return '';
    }
    Config::set('app_trace', false);
    return view(app()->getRootPath() . 'public/pc/index.html');
})->pattern(['any' => '\w+']);

//定时任务
Route::rule('crontab', function () {
    think\Console::call('crontab');
});

// 后台管理海报模块路由
Route::group('admin', function () {
    // 海报管理路由组
    Route::group('poster_manage', function () {
        // 配置管理
        Route::rule('config_list', 'admin/PosterManage/configList');
        Route::rule('add_config', 'admin/PosterManage/addConfig');
        Route::rule('edit_config', 'admin/PosterManage/editConfig');
        Route::rule('del_config', 'admin/PosterManage/delConfig');
        Route::rule('config_detail', 'admin/PosterManage/configDetail');
        Route::rule('switch_status', 'admin/PosterManage/switchStatus');
        Route::rule('copy_config', 'admin/PosterManage/copyConfig');
        Route::rule('parse_template', 'admin/PosterManage/parseTemplate');

        // 模板选择相关路由
        Route::rule('template_select', 'admin/PosterManage/templateSelect');
        Route::rule('get_template_detail', 'admin/PosterManage/getTemplateDetail');
        Route::rule('check_database', 'admin/PosterManage/checkDatabase');
        Route::rule('clear_cache', 'admin/PosterManage/clearCache');
        Route::rule('test_api', 'admin/PosterManage/testApi');
        Route::rule('test_real_api', 'admin/PosterManage/testRealApi');
        Route::rule('test_table', 'admin/PosterManage/testTable');

        // 数据管理
        Route::rule('data_list', 'admin/PosterManage/dataList');
        Route::rule('data_detail', 'admin/PosterManage/dataDetail');
        Route::rule('del_data', 'admin/PosterManage/delData');
        Route::rule('batch_del_data', 'admin/PosterManage/batchDelData');
        Route::rule('export_data', 'admin/PosterManage/exportData');

        // 统计分析
        Route::rule('statistics', 'admin/PosterManage/statistics');
    });
});