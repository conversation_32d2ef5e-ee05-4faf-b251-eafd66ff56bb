# 动态参数模板系统测试指南

## 测试环境准备

### 1. 数据库初始化

```bash
# 方法1：通过MySQL命令行
mysql -u your_username -p your_database < database/migrations/create_poster_template_tables.sql

# 方法2：登录MySQL后执行
mysql -u your_username -p
use your_database;
source database/migrations/create_poster_template_tables.sql;
```

### 2. 环境配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

修改 `.env` 文件中的配置：
- `XUNPAI_BASE_URL`: 迅排设计服务地址
- 数据库连接信息
- Redis配置（如果使用）

### 3. 确保Web服务运行

确保您的Web服务器（Apache/Nginx）和PHP-FPM正在运行，并且可以访问项目。

## 测试执行步骤

### 步骤1：基础系统测试

```bash
# 在项目根目录执行
php tests/poster_system_test.php
```

这个测试会验证：
- ✅ 数据库连接
- ✅ 模型创建和ID生成
- ✅ 外部API健康检查
- ✅ 模板配置CRUD操作
- ✅ 用户数据CRUD操作
- ✅ 迅排设计API服务连接
- ✅ 完整业务流程

**预期结果**：
```
=== 动态参数模板系统测试开始 ===

测试: 数据库连接测试
✅ 通过

测试: 模型创建测试
生成的ID示例:
配置ID: config_20250117123456_1234
数据ID: user_data_20250117123456_5678
记录ID: record_20250117123456_9012
✅ 通过

...

=== 测试结果 ===
通过: 7
失败: 0
总计: 7
```

### 步骤2：最终测试报告

```bash
# 在项目根目录执行
php tests/final_test_report.php
```

这个测试会验证：
- ✅ 数据库连接和表结构
- ✅ 模型类和ID生成器
- ✅ API接口功能
- ✅ 业务流程完整性
- ✅ 安全性和权限控制

**预期结果**：
```
=== 动态参数模板系统最终测试报告 ===

测试: 数据库连接测试
✅ 通过

测试: API接口测试
✅ 通过

...

=== 测试结果 ===
通过: 8
失败: 0
总计: 8
```

### 步骤3：Postman接口测试

1. **导入Postman集合**：
   - 打开Postman
   - 点击 Import
   - 选择 `tests/postman_collection.json` 文件

2. **设置环境变量**：
   - 在Postman中创建新环境
   - 设置变量：
     - `base_url`: `http://localhost` (您的域名)
     - `api_key`: `poster_api_key_2025_secure_token_12345`

3. **按顺序执行测试**：
   - 外部API测试 → 健康检查
   - 内部API测试 → 解析模板
   - 内部API测试 → 创建配置（记录返回的config_id）
   - 内部API测试 → 提交用户数据（记录返回的data_id）
   - 外部API测试 → 获取参数配置
   - 外部API测试 → 获取参数数据

### 步骤4：与迅排设计服务联调测试

**前提条件**：迅排设计服务必须运行在 `http://localhost:7001`

1. **启动迅排设计服务**：
```bash
cd /path/to/poster-design
npm run serve
```

2. **配置迅排设计服务**：
在迅排设计的 `service/.env` 文件中添加：
```env
EXTERNAL_API_URL=http://localhost/api
EXTERNAL_API_KEY=poster_api_key_2025_secure_token_12345
```

3. **执行联调测试**：
```bash
# 测试迅排设计调用主项目API
curl -X GET "http://localhost:7001/api/template/parse" \
  -H "Content-Type: application/json" \
  -d '{"templateId": "2"}'
```

## 测试检查清单

### ✅ 基础功能测试

- [ ] 数据库表创建成功
- [ ] 模型类正常工作
- [ ] ID生成器正常工作
- [ ] 配置文件加载正常

### ✅ API接口测试

- [ ] 外部健康检查API返回200
- [ ] 外部参数数据API正常返回数据
- [ ] 外部参数配置API正常返回配置
- [ ] 内部模板解析API正常工作
- [ ] 内部配置管理API正常工作
- [ ] 内部用户数据API正常工作

### ✅ 业务流程测试

- [ ] 模板配置创建流程正常
- [ ] 用户数据提交流程正常
- [ ] 参数验证机制正常
- [ ] 数据关联关系正确

### ✅ 安全性测试

- [ ] API Key认证正常工作
- [ ] 无效API Key被拒绝
- [ ] 参数验证防止恶意输入
- [ ] 数据访问权限控制正常

### ✅ 性能测试

- [ ] API响应时间在可接受范围内
- [ ] 缓存机制正常工作
- [ ] 数据库查询性能正常
- [ ] 并发请求处理正常

## 常见问题排查

### 1. 数据库连接失败

**症状**：测试显示数据库连接失败
**解决方案**：
- 检查数据库配置信息
- 确认数据库服务正在运行
- 检查用户权限

### 2. API Key认证失败

**症状**：外部API返回401错误
**解决方案**：
- 检查API Key是否正确
- 确认数据库中有对应的API Key记录
- 检查请求头格式是否正确

### 3. 迅排设计服务连接失败

**症状**：迅排设计API服务测试失败
**解决方案**：
- 确认迅排设计服务正在运行
- 检查服务地址配置
- 检查网络连接

### 4. 模型操作失败

**症状**：CRUD操作失败
**解决方案**：
- 检查数据库表结构
- 确认字段类型匹配
- 检查外键约束

## 测试数据清理

测试完成后，可以执行以下SQL清理测试数据：

```sql
-- 清理测试数据
DELETE FROM ls_poster_generation_records WHERE data_id LIKE '%test%';
DELETE FROM ls_poster_user_data WHERE config_id LIKE '%test%';
DELETE FROM ls_poster_template_configs WHERE config_name LIKE '%测试%' OR config_name LIKE '%test%';
```

## 下一步

测试通过后，您可以：

1. **部署到生产环境**
2. **开发前端管理界面**
3. **完善用户界面**
4. **添加更多功能特性**
5. **性能优化和监控**

## 技术支持

如果测试过程中遇到问题，请检查：

1. **日志文件**：`runtime/log/` 目录下的日志
2. **错误信息**：详细的错误堆栈信息
3. **配置文件**：确认所有配置项正确
4. **依赖服务**：确认所有依赖服务正常运行
