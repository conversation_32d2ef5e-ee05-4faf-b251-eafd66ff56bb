{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>编辑海报模板配置</span>
            <a class="layui-btn layui-btn-sm layui-btn-primary fr" href="{:url('config_list')}">
                <i class="layui-icon layui-icon-return"></i> 返回列表
            </a>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" action="{:url('edit_config')}" method="post" lay-filter="configForm">
                <input type="hidden" name="id" value="{$detail.id}">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md8">
                        <div class="layui-form-item">
                            <label class="layui-form-label">模板ID <span class="required">*</span></label>
                            <div class="layui-input-block">
                                <input type="text" name="template_id" required lay-verify="required" 
                                       placeholder="请输入模板ID" autocomplete="off" class="layui-input" value="{$detail.template_id}">
                                <div class="layui-form-mid layui-word-aux">
                                    <a href="javascript:;" id="parseTemplate" class="layui-btn layui-btn-xs layui-btn-normal">重新解析模板</a>
                                </div>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">模板标题</label>
                            <div class="layui-input-block">
                                <input type="text" name="template_title" placeholder="请输入模板标题" 
                                       autocomplete="off" class="layui-input" value="{$detail.template_title}">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">配置名称 <span class="required">*</span></label>
                            <div class="layui-input-block">
                                <input type="text" name="config_name" required lay-verify="required" 
                                       placeholder="请输入配置名称" autocomplete="off" class="layui-input" value="{$detail.config_name}">
                            </div>
                        </div>

                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">配置描述</label>
                            <div class="layui-input-block">
                                <textarea name="config_description" placeholder="请输入配置描述" 
                                          class="layui-textarea">{$detail.config_description}</textarea>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <input type="radio" name="status" value="1" title="启用" {if condition="$detail.status == 1"}checked{/if}>
                                <input type="radio" name="status" value="0" title="禁用" {if condition="$detail.status == 0"}checked{/if}>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">参数配置</label>
                            <div class="layui-input-block">
                                <div id="parametersContainer">
                                    <!-- 参数配置项将在这里动态生成 -->
                                </div>
                                <button type="button" class="layui-btn layui-btn-normal" id="addParameter">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加参数
                                </button>
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="configSubmit">保存修改</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                <a href="{:url('config_list')}" class="layui-btn layui-btn-primary">取消</a>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md4">
                        <div class="layui-card">
                            <div class="layui-card-header">配置信息</div>
                            <div class="layui-card-body">
                                <p><strong>配置ID:</strong> {$detail.id}</p>
                                <p><strong>创建时间:</strong> {$detail.create_time|date='Y-m-d H:i:s',###}</p>
                                <p><strong>更新时间:</strong> {$detail.update_time|date='Y-m-d H:i:s',###}</p>
                                <p><strong>创建者:</strong> {$detail.created_by|default='未知'}</p>
                            </div>
                        </div>

                        <div class="layui-card">
                            <div class="layui-card-header">使用说明</div>
                            <div class="layui-card-body">
                                <p><strong>模板ID：</strong>迅排设计服务中的模板标识</p>
                                <p><strong>配置名称：</strong>用于区分不同的参数配置</p>
                                <p><strong>参数配置：</strong>定义用户可以填写的参数</p>
                                <hr>
                                <p><strong>参数类型说明：</strong></p>
                                <ul>
                                    <li>text - 文本输入</li>
                                    <li>number - 数字输入</li>
                                    <li>email - 邮箱输入</li>
                                    <li>url - 网址输入</li>
                                    <li>textarea - 多行文本</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 参数配置模板 -->
<script type="text/html" id="parameterTemplate">
    <div class="parameter-item layui-card" style="margin-bottom: 15px;" data-index="{{index}}">
        <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="layui-form-label">参数名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][parameterName]" 
                               placeholder="参数名称" class="layui-input" value="{{parameterName}}">
                    </div>
                </div>
                <div class="layui-col-md6">
                    <label class="layui-form-label">参数标签</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][parameterLabel]" 
                               placeholder="显示标签" class="layui-input" value="{{parameterLabel}}">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <label class="layui-form-label">参数类型</label>
                    <div class="layui-input-block">
                        <select name="parameters[{{index}}][parameterType]">
                            <option value="text" {{# if(d.parameterType === 'text') { }}selected{{# } }}>文本</option>
                            <option value="number" {{# if(d.parameterType === 'number') { }}selected{{# } }}>数字</option>
                            <option value="email" {{# if(d.parameterType === 'email') { }}selected{{# } }}>邮箱</option>
                            <option value="url" {{# if(d.parameterType === 'url') { }}selected{{# } }}>网址</option>
                            <option value="textarea" {{# if(d.parameterType === 'textarea') { }}selected{{# } }}>多行文本</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">是否必填</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="parameters[{{index}}][isRequired]" 
                               {{# if(d.isRequired) { }}checked{{# } }} lay-skin="switch" lay-text="是|否">
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">是否启用</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="parameters[{{index}}][isEnabled]" 
                               {{# if(d.isEnabled !== false) { }}checked{{# } }} lay-skin="switch" lay-text="是|否">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="layui-form-label">元素UUID</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][elementUuid]" 
                               placeholder="元素UUID" class="layui-input" value="{{elementUuid}}">
                    </div>
                </div>
                <div class="layui-col-md6">
                    <label class="layui-form-label">显示顺序</label>
                    <div class="layui-input-block">
                        <input type="number" name="parameters[{{index}}][displayOrder]" 
                               placeholder="显示顺序" class="layui-input" value="{{displayOrder}}">
                    </div>
                </div>
            </div>
            <input type="hidden" name="parameters[{{index}}][id]" value="{{id}}">
            <div style="text-align: right; margin-top: 10px;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-parameter">
                    <i class="layui-icon layui-icon-delete"></i> 删除
                </button>
            </div>
        </div>
    </div>
</script>

<script>
layui.use(['form', 'layer', 'laytpl'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;

    var parameterIndex = 0;

    // 页面加载时渲染现有参数
    var existingParameters = {$detail.parameters_array|json_encode};
    if (existingParameters && existingParameters.length > 0) {
        existingParameters.forEach(function(param) {
            addParameter(param);
        });
    }

    // 添加参数
    $('#addParameter').click(function(){
        addParameter();
    });

    // 添加参数函数
    function addParameter(paramData) {
        var data = paramData || {
            index: parameterIndex++,
            id: '',
            elementUuid: '',
            parameterName: '',
            parameterLabel: '',
            parameterType: 'text',
            isRequired: false,
            isEnabled: true,
            displayOrder: parameterIndex
        };
        data.index = parameterIndex++;

        var getTpl = document.getElementById('parameterTemplate').innerHTML;
        var view = document.getElementById('parametersContainer');
        laytpl(getTpl).render(data, function(html){
            view.insertAdjacentHTML('beforeend', html);
            form.render();
        });
    }

    // 删除参数
    $(document).on('click', '.remove-parameter', function(){
        $(this).closest('.parameter-item').remove();
    });

    // 解析模板
    $('#parseTemplate').click(function(){
        var templateId = $('input[name="template_id"]').val();
        if (!templateId) {
            layer.msg('请先输入模板ID', {icon: 2});
            return;
        }

        layer.confirm('重新解析模板将清空现有参数配置，确定继续吗？', {icon: 3, title:'确认解析'}, function(index){
            var loadingIndex = layer.load(2, {content: '解析中...'});
            
            $.post('{:url("parse_template")}', {template_id: templateId}, function(res){
                layer.close(loadingIndex);
                if(res.code == 1){
                    // 填充模板标题
                    if (res.data.template_title) {
                        $('input[name="template_title"]').val(res.data.template_title);
                    }
                    
                    // 清空现有参数
                    $('#parametersContainer').empty();
                    parameterIndex = 0;
                    
                    // 添加解析出的参数
                    if (res.data.available_parameters && res.data.available_parameters.length > 0) {
                        res.data.available_parameters.forEach(function(param) {
                            addParameter({
                                id: param.id,
                                elementUuid: param.elementUuid,
                                parameterName: param.parameterName,
                                parameterLabel: param.parameterLabel,
                                parameterType: param.parameterType || 'text',
                                isRequired: false,
                                isEnabled: true,
                                displayOrder: parameterIndex + 1
                            });
                        });
                    }
                    
                    layer.msg('模板解析成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '解析失败', {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    // 表单提交
    form.on('submit(configSubmit)', function(data){
        var loadingIndex = layer.load(2, {content: '保存中...'});
        
        $.post('{:url("edit_config")}', data.field, function(res){
            layer.close(loadingIndex);
            if(res.code == 1){
                layer.msg('保存成功', {icon: 1}, function(){
                    location.href = '{:url("config_list")}';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });
});
</script>
</div>
