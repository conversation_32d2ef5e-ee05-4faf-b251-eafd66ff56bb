{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>添加海报模板配置</span>
        <a class="layui-btn layui-btn-sm layui-btn-primary fr" href="{:url('config_list')}">
            <i class="layui-icon layui-icon-return"></i> 返回列表
        </a>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="{:url('add_config')}" method="post" lay-filter="configForm">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md8">
                    <div class="layui-form-item">
                        <label class="layui-form-label">选择模板 <span class="required">*</span></label>
                        <div class="layui-input-block">
                            <div class="template-selector">
                                <input type="hidden" name="template_id" required lay-verify="required">
                                <div class="selected-template" id="selectedTemplate" style="display: none;">
                                    <div class="template-preview">
                                        <img id="templateThumbnail" src="" alt="模板预览" style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px;">
                                        <div class="template-info">
                                            <h4 id="templateTitle"></h4>
                                            <p id="templateSize"></p>
                                            <p>模板ID: <span id="templateId"></span></p>
                                        </div>
                                        <div class="template-actions">
                                            <a href="javascript:;" id="changeTemplate" class="layui-btn layui-btn-xs layui-btn-normal">更换模板</a>
                                            <a href="javascript:;" id="parseTemplate" class="layui-btn layui-btn-xs layui-btn-warm">解析参数</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="select-template-btn" id="selectTemplateBtn">
                                    <button type="button" class="layui-btn layui-btn-primary layui-btn-fluid">
                                        <i class="layui-icon layui-icon-add-1"></i> 选择模板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">模板标题</label>
                        <div class="layui-input-block">
                            <input type="text" name="template_title" placeholder="请输入模板标题" 
                                   autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">配置名称 <span class="required">*</span></label>
                        <div class="layui-input-block">
                            <input type="text" name="config_name" required lay-verify="required" 
                                   placeholder="请输入配置名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">配置描述</label>
                        <div class="layui-input-block">
                            <textarea name="config_description" placeholder="请输入配置描述" 
                                      class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block">
                            <input type="radio" name="status" value="1" title="启用" checked>
                            <input type="radio" name="status" value="0" title="禁用">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">参数配置</label>
                        <div class="layui-input-block">
                            <div id="parametersContainer">
                                <!-- 参数配置项将在这里动态生成 -->
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" id="addParameter">
                                <i class="layui-icon layui-icon-add-1"></i> 添加参数
                            </button>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="configSubmit">保存配置</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header">使用说明</div>
                        <div class="layui-card-body">
                            <p><strong>模板ID：</strong>迅排设计服务中的模板标识</p>
                            <p><strong>配置名称：</strong>用于区分不同的参数配置</p>
                            <p><strong>参数配置：</strong>定义用户可以填写的参数</p>
                            <hr>
                            <p><strong>参数类型说明：</strong></p>
                            <ul>
                                <li>text - 文本输入</li>
                                <li>number - 数字输入</li>
                                <li>email - 邮箱输入</li>
                                <li>url - 网址输入</li>
                                <li>textarea - 多行文本</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 参数配置模板 -->
<script type="text/html" id="parameterTemplate">
    <div class="parameter-item layui-card" style="margin-bottom: 15px;" data-index="{{index}}">
        <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="layui-form-label">参数名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][parameterName]" 
                               placeholder="参数名称" class="layui-input" value="{{parameterName}}">
                    </div>
                </div>
                <div class="layui-col-md6">
                    <label class="layui-form-label">参数标签</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][parameterLabel]" 
                               placeholder="显示标签" class="layui-input" value="{{parameterLabel}}">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <label class="layui-form-label">参数类型</label>
                    <div class="layui-input-block">
                        <select name="parameters[{{index}}][parameterType]">
                            <option value="text" {{# if(d.parameterType === 'text') { }}selected{{# } }}>文本</option>
                            <option value="number" {{# if(d.parameterType === 'number') { }}selected{{# } }}>数字</option>
                            <option value="email" {{# if(d.parameterType === 'email') { }}selected{{# } }}>邮箱</option>
                            <option value="url" {{# if(d.parameterType === 'url') { }}selected{{# } }}>网址</option>
                            <option value="textarea" {{# if(d.parameterType === 'textarea') { }}selected{{# } }}>多行文本</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">是否必填</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="parameters[{{index}}][isRequired]" 
                               {{# if(d.isRequired) { }}checked{{# } }} lay-skin="switch" lay-text="是|否">
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">是否启用</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="parameters[{{index}}][isEnabled]" 
                               {{# if(d.isEnabled !== false) { }}checked{{# } }} lay-skin="switch" lay-text="是|否">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="layui-form-label">元素UUID</label>
                    <div class="layui-input-block">
                        <input type="text" name="parameters[{{index}}][elementUuid]" 
                               placeholder="元素UUID" class="layui-input" value="{{elementUuid}}">
                    </div>
                </div>
                <div class="layui-col-md6">
                    <label class="layui-form-label">显示顺序</label>
                    <div class="layui-input-block">
                        <input type="number" name="parameters[{{index}}][displayOrder]" 
                               placeholder="显示顺序" class="layui-input" value="{{displayOrder}}">
                    </div>
                </div>
            </div>
            <input type="hidden" name="parameters[{{index}}][id]" value="{{id}}">
            <div style="text-align: right; margin-top: 10px;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-parameter">
                    <i class="layui-icon layui-icon-delete"></i> 删除
                </button>
            </div>
        </div>
    </div>
</script>

</div>

<style>
.template-selector {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s;
}

.template-selector:hover {
    border-color: #1890ff;
}

.selected-template {
    text-align: left;
}

.template-preview {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.template-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.template-info p {
    margin: 0 0 3px 0;
    color: #666;
    font-size: 14px;
}

.template-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: auto;
}

.select-template-btn {
    padding: 40px 20px;
}

.required {
    color: #ff4d4f;
}
</style>

<script>
layui.use(['form', 'layer', 'laytpl', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;

    var parameterIndex = 0;

    // 初始化：检查URL参数中是否有模板信息
    initializeFromUrl();

    function initializeFromUrl() {
        var urlParams = new URLSearchParams(window.location.search);
        var templateId = urlParams.get('template_id');
        var templateTitle = urlParams.get('template_title');

        if (templateId && templateTitle) {
            // 设置模板信息
            setSelectedTemplate({
                id: templateId,
                title: decodeURIComponent(templateTitle),
                thumbnail: '', // 需要通过API获取
                width: '',
                height: ''
            });

            // 自动解析模板参数
            setTimeout(function() {
                parseTemplateParameters();
            }, 500);
        }
    }

    // 设置选中的模板
    function setSelectedTemplate(template) {
        $('input[name="template_id"]').val(template.id);
        $('input[name="template_title"]').val(template.title);
        $('#templateId').text(template.id);
        $('#templateTitle').text(template.title);

        if (template.width && template.height) {
            $('#templateSize').text(template.width + ' × ' + template.height);
        }

        if (template.thumbnail) {
            $('#templateThumbnail').attr('src', template.thumbnail);
        } else {
            $('#templateThumbnail').attr('src', '/static/admin/images/index.png');
        }

        $('#selectedTemplate').show();
        $('#selectTemplateBtn').hide();

        // 触发表单验证
        form.render();
    }

    // 清除选中的模板
    function clearSelectedTemplate() {
        $('input[name="template_id"]').val('');
        $('input[name="template_title"]').val('');
        $('#selectedTemplate').hide();
        $('#selectTemplateBtn').show();

        // 清空参数配置
        $('#parametersContainer').empty();
        parameterIndex = 0;
    }

    // 选择模板按钮点击事件
    $('#selectTemplateBtn, #changeTemplate').click(function(){
        layer.open({
            type: 2,
            title: '选择海报模板',
            content: '{:url("template_select")}',
            area: ['90%', '80%'],
            maxmin: true
        });
    });

    // 监听来自模板选择页面的消息
    window.addEventListener('message', function(event) {
        if (event.data.type === 'templateSelected') {
            var templateData = event.data.data;

            // 获取模板详情
            $.get('{:url("get_template_detail")}', {template_id: templateData.template_id}, function(res){
                if(res.code == 1) {
                    setSelectedTemplate({
                        id: templateData.template_id,
                        title: templateData.template_title,
                        thumbnail: res.data.thumbnail || res.data.cover,
                        width: res.data.width,
                        height: res.data.height
                    });
                } else {
                    setSelectedTemplate({
                        id: templateData.template_id,
                        title: templateData.template_title,
                        thumbnail: '',
                        width: '',
                        height: ''
                    });
                }
            });

            layer.closeAll();
        }
    });

    // 添加参数
    $('#addParameter').click(function(){
        addParameter();
    });

    // 添加参数函数
    function addParameter(paramData) {
        var data = paramData || {
            index: parameterIndex++,
            id: '',
            elementUuid: '',
            parameterName: '',
            parameterLabel: '',
            parameterType: 'text',
            isRequired: false,
            isEnabled: true,
            displayOrder: parameterIndex
        };
        data.index = parameterIndex++;

        var getTpl = document.getElementById('parameterTemplate').innerHTML;
        var view = document.getElementById('parametersContainer');
        laytpl(getTpl).render(data, function(html){
            view.insertAdjacentHTML('beforeend', html);
            form.render();
        });
    }

    // 删除参数
    $(document).on('click', '.remove-parameter', function(){
        $(this).closest('.parameter-item').remove();
    });

    // 解析模板参数
    function parseTemplateParameters() {
        var templateId = $('input[name="template_id"]').val();
        if (!templateId) {
            layer.msg('请先选择模板', {icon: 2});
            return;
        }

        var loadingIndex = layer.load(2, {content: '解析中...'});

        $.post('{:url("parse_template")}', {template_id: templateId}, function(res){
            layer.close(loadingIndex);
            if(res.code == 1){
                // 填充模板标题
                if (res.data.template_title) {
                    $('input[name="template_title"]').val(res.data.template_title);
                }

                // 清空现有参数
                $('#parametersContainer').empty();
                parameterIndex = 0;

                // 添加解析出的参数
                if (res.data.available_parameters && res.data.available_parameters.length > 0) {
                    res.data.available_parameters.forEach(function(param) {
                        addParameter({
                            id: param.id,
                            elementUuid: param.elementUuid,
                            parameterName: param.parameterName,
                            parameterLabel: param.parameterLabel,
                            parameterType: param.parameterType || 'text',
                            isRequired: false,
                            isEnabled: true,
                            displayOrder: parameterIndex + 1
                        });
                    });
                } else {
                    // 如果没有解析到参数，添加一个默认参数
                    addParameter();
                }

                layer.msg('模板解析成功', {icon: 1});
            } else {
                layer.msg(res.msg || '解析失败', {icon: 2});
                // 解析失败时也添加一个默认参数
                addParameter();
            }
        });
    }

    // 解析模板按钮点击事件
    $(document).on('click', '#parseTemplate', function(){
        parseTemplateParameters();
    });

    // 表单提交
    form.on('submit(configSubmit)', function(data){
        var loadingIndex = layer.load(2, {content: '保存中...'});
        
        $.post('{:url("add_config")}', data.field, function(res){
            layer.close(loadingIndex);
            if(res.code == 1){
                layer.msg('保存成功', {icon: 1}, function(){
                    location.href = '{:url("config_list")}';
                });
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });

    // 如果没有从URL初始化模板，默认添加一个参数
    if (!$('input[name="template_id"]').val()) {
        addParameter();
    }
});
</script>
