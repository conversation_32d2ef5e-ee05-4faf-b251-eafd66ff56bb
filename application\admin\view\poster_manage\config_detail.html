{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>配置详情 - {$detail.config_name}</span>
            <div class="layui-btn-group fr">
                <a class="layui-btn layui-btn-sm" href="{:url('edit_config', ['id' => $detail.id])}">
                    <i class="layui-icon layui-icon-edit"></i> 编辑配置
                </a>
                <a class="layui-btn layui-btn-sm layui-btn-primary" href="{:url('config_list')}">
                    <i class="layui-icon layui-icon-return"></i> 返回列表
                </a>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <!-- 基本信息 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">基本信息</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr>
                                        <td width="120"><strong>配置ID</strong></td>
                                        <td>{$detail.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>配置名称</strong></td>
                                        <td>{$detail.config_name}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>模板ID</strong></td>
                                        <td>{$detail.template_id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>模板标题</strong></td>
                                        <td>{$detail.template_title|default='未设置'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>配置描述</strong></td>
                                        <td>{$detail.config_description|default='无描述'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态</strong></td>
                                        <td>
                                            {if condition="$detail.status == 1"}
                                            <span class="layui-badge layui-bg-green">启用</span>
                                            {else/}
                                            <span class="layui-badge">禁用</span>
                                            {/if}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>参数数量</strong></td>
                                        <td>{$detail.parameters_array|count} 个</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间</strong></td>
                                        <td>{$detail.create_time|date='Y-m-d H:i:s',###}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间</strong></td>
                                        <td>{$detail.update_time|date='Y-m-d H:i:s',###}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建者</strong></td>
                                        <td>{$detail.created_by|default='未知'}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 使用统计 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">使用统计</div>
                        <div class="layui-card-body">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div style="text-align: center; padding: 20px;">
                                        <div style="font-size: 24px; color: #1E9FFF;" id="totalUsage">0</div>
                                        <div style="color: #666;">总使用次数</div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div style="text-align: center; padding: 20px;">
                                        <div style="font-size: 24px; color: #5FB878;" id="todayUsage">0</div>
                                        <div style="color: #666;">今日使用</div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md6">
                                    <div style="text-align: center; padding: 20px;">
                                        <div style="font-size: 24px; color: #FFB800;" id="draftCount">0</div>
                                        <div style="color: #666;">草稿数量</div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div style="text-align: center; padding: 20px;">
                                        <div style="font-size: 24px; color: #FF5722;" id="submittedCount">0</div>
                                        <div style="color: #666;">已提交数量</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 参数配置详情 -->
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">参数配置详情</div>
                        <div class="layui-card-body">
                            {if condition="$detail.parameters_array && count($detail.parameters_array) > 0"}
                            <table class="layui-table" lay-size="sm">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>参数标签</th>
                                        <th>参数类型</th>
                                        <th>是否必填</th>
                                        <th>是否启用</th>
                                        <th>元素UUID</th>
                                        <th>显示顺序</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {volist name="detail.parameters_array" id="param" key="k"}
                                    <tr>
                                        <td>{$k}</td>
                                        <td><code>{$param.parameterName}</code></td>
                                        <td>{$param.parameterLabel}</td>
                                        <td>
                                            {switch name="param.parameterType"}
                                            {case value="text"}文本{/case}
                                            {case value="number"}数字{/case}
                                            {case value="email"}邮箱{/case}
                                            {case value="url"}网址{/case}
                                            {case value="textarea"}多行文本{/case}
                                            {default/}{$param.parameterType}
                                            {/switch}
                                        </td>
                                        <td>
                                            {if condition="$param.isRequired"}
                                            <span class="layui-badge layui-bg-orange">必填</span>
                                            {else/}
                                            <span class="layui-badge layui-bg-gray">可选</span>
                                            {/if}
                                        </td>
                                        <td>
                                            {if condition="$param.isEnabled"}
                                            <span class="layui-badge layui-bg-green">启用</span>
                                            {else/}
                                            <span class="layui-badge">禁用</span>
                                            {/if}
                                        </td>
                                        <td><small>{$param.elementUuid|default='未设置'}</small></td>
                                        <td>{$param.displayOrder|default=1}</td>
                                    </tr>
                                    {/volist}
                                </tbody>
                            </table>
                            {else/}
                            <div style="text-align: center; padding: 50px; color: #999;">
                                <i class="layui-icon layui-icon-face-cry" style="font-size: 48px;"></i>
                                <p>暂无参数配置</p>
                                <a href="{:url('edit_config', ['id' => $detail.id])}" class="layui-btn layui-btn-sm">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加参数
                                </a>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">快速操作</div>
                        <div class="layui-card-body">
                            <div style="text-align: center; padding: 20px;">
                                <a href="{:url('edit_config', ['id' => $detail.id])}" class="layui-btn">
                                    <i class="layui-icon layui-icon-edit"></i> 编辑配置
                                </a>
                                <a href="{:url('data_list', ['config_id' => $detail.id])}" class="layui-btn layui-btn-normal">
                                    <i class="layui-icon layui-icon-table"></i> 查看数据
                                </a>
                                <button class="layui-btn layui-btn-warm" onclick="copyConfig()">
                                    <i class="layui-icon layui-icon-file"></i> 复制配置
                                </button>
                                <button class="layui-btn layui-btn-primary" onclick="testConfig()">
                                    <i class="layui-icon layui-icon-play"></i> 测试配置
                                </button>
                                {if condition="$detail.status == 1"}
                                <button class="layui-btn layui-btn-disabled" onclick="switchStatus(0)">
                                    <i class="layui-icon layui-icon-pause"></i> 禁用配置
                                </button>
                                {else/}
                                <button class="layui-btn layui-btn-normal" onclick="switchStatus(1)">
                                    <i class="layui-icon layui-icon-play"></i> 启用配置
                                </button>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 页面加载时获取使用统计
    loadUsageStatistics();

    // 加载使用统计
    function loadUsageStatistics() {
        $.get('{:url("data_list")}', {config_id: '{$detail.id}'}, function(res){
            if(res.code == 1){
                $('#totalUsage').text(res.data.count || 0);
                
                // 计算草稿和已提交数量
                var draftCount = 0;
                var submittedCount = 0;
                if(res.data.lists && res.data.lists.length > 0){
                    res.data.lists.forEach(function(item){
                        if(item.is_draft == 1){
                            draftCount++;
                        } else {
                            submittedCount++;
                        }
                    });
                }
                $('#draftCount').text(draftCount);
                $('#submittedCount').text(submittedCount);
                
                // 模拟今日使用数据
                $('#todayUsage').text(Math.floor(Math.random() * 10));
            }
        }).fail(function(){
            // 显示模拟数据
            $('#totalUsage').text('--');
            $('#todayUsage').text('--');
            $('#draftCount').text('--');
            $('#submittedCount').text('--');
        });
    }

    // 复制配置
    window.copyConfig = function() {
        layer.confirm('确定要复制这个配置吗？', {icon: 3, title:'确认复制'}, function(index){
            $.post('{:url("copy_config")}', {id: '{$detail.id}'}, function(res){
                if(res.code == 1){
                    layer.msg('复制成功', {icon: 1}, function(){
                        location.href = '{:url("edit_config")}?id=' + res.data.id;
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    };

    // 测试配置
    window.testConfig = function() {
        var testUrl = '/user/poster_data_submit.html?config_id={$detail.id}';
        layer.confirm('将在新窗口打开用户填写页面进行测试，确定继续吗？', {icon: 3, title:'测试配置'}, function(index){
            window.open(testUrl, '_blank');
            layer.close(index);
        });
    };

    // 切换状态
    window.switchStatus = function(status) {
        var text = status == 1 ? '启用' : '禁用';
        layer.confirm('确定要' + text + '这个配置吗？', {icon: 3, title:'确认' + text}, function(index){
            $.post('{:url("switch_status")}', {id: '{$detail.id}', status: status}, function(res){
                if(res.code == 1){
                    layer.msg(text + '成功', {icon: 1}, function(){
                        location.reload();
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    };
});
</script>
</div>
