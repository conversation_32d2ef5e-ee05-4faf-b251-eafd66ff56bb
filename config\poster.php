<?php
/**
 * 动态参数模板系统配置文件
 */

return [
    // 迅排设计服务配置
    'xunpai_service' => [
        'base_url' => env('XUNPAI_BASE_URL', 'http://localhost:7001'),
        'api_timeout' => env('XUNPAI_API_TIMEOUT', 10), // 秒
        'retry_attempts' => env('XUNPAI_RETRY_ATTEMPTS', 3),
        'cache_enabled' => env('XUNPAI_CACHE_ENABLED', true),
        'cache_ttl' => env('XUNPAI_CACHE_TTL', 3600), // 秒
    ],

    // API安全配置
    'api_security' => [
        'rate_limit' => [
            'enabled' => true,
            'max_requests' => 100, // 每分钟最大请求数
            'window' => 60, // 时间窗口（秒）
        ],
        'allowed_ips' => [
            // 允许访问的IP地址，空数组表示不限制
            // '127.0.0.1',
            // '*************',
        ],
    ],

    // 参数验证配置
    'parameter_validation' => [
        'max_text_length' => 500,
        'max_textarea_length' => 2000,
        'allowed_html_tags' => ['br', 'strong', 'em', 'span'],
        'forbidden_words' => [
            // 敏感词列表
            // '敏感词1', '敏感词2'
        ],
    ],

    // 文件存储配置
    'file_storage' => [
        'upload_path' => 'uploads/poster/',
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
        'max_file_size' => 10 * 1024 * 1024, // 10MB
    ],

    // 缓存配置
    'cache' => [
        'prefix' => 'poster_',
        'template_config_ttl' => 3600, // 模板配置缓存时间
        'user_data_ttl' => 1800, // 用户数据缓存时间
    ],

    // 日志配置
    'log' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'file' => 'poster_system.log',
    ],

    // 默认参数类型配置
    'parameter_types' => [
        'text' => [
            'label' => '单行文本',
            'validation' => ['required', 'max:200'],
            'input_type' => 'text',
        ],
        'textarea' => [
            'label' => '多行文本',
            'validation' => ['required', 'max:1000'],
            'input_type' => 'textarea',
        ],
        'number' => [
            'label' => '数字',
            'validation' => ['required', 'numeric'],
            'input_type' => 'number',
        ],
        'email' => [
            'label' => '邮箱地址',
            'validation' => ['required', 'email'],
            'input_type' => 'email',
        ],
        'phone' => [
            'label' => '手机号码',
            'validation' => ['required', 'regex:/^1[3-9]\d{9}$/'],
            'input_type' => 'tel',
        ],
        'url' => [
            'label' => '网址链接',
            'validation' => ['required', 'url'],
            'input_type' => 'url',
        ],
    ],

    // 模板分类配置
    'template_categories' => [
        'business_card' => '个人名片',
        'event_poster' => '活动海报',
        'product_promotion' => '产品宣传',
        'holiday_greeting' => '节日祝福',
        'company_promotion' => '企业宣传',
        'daily_quote' => '每日一签',
    ],

    // 批量生成配置
    'batch_generation' => [
        'max_batch_size' => 50,
        'queue_enabled' => true,
        'timeout_per_item' => 30, // 秒
    ],
];
