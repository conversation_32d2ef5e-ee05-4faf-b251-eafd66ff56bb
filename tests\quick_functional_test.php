<?php
/**
 * 快速功能测试脚本
 * 用于快速验证系统基础功能是否正常
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入ThinkPHP框架
require_once __DIR__ . '/../thinkphp/base.php';

// 设置应用路径
define('APP_PATH', __DIR__ . '/../application/');
define('ROOT_PATH', __DIR__ . '/../');

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "=== 快速功能测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

$tests = [
    'database' => '数据库连接',
    'models' => '模型功能',
    'config' => '配置加载',
    'api_health' => 'API健康检查',
];

$results = [];

foreach ($tests as $key => $name) {
    echo "测试: {$name}... ";
    
    try {
        $result = false;
        
        switch ($key) {
            case 'database':
                $result = testDatabase();
                break;
            case 'models':
                $result = testModels();
                break;
            case 'config':
                $result = testConfig();
                break;
            case 'api_health':
                $result = testApiHealth();
                break;
        }
        
        if ($result) {
            echo "✅ 通过\n";
            $results[$key] = 'PASS';
        } else {
            echo "❌ 失败\n";
            $results[$key] = 'FAIL';
        }
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "\n";
        $results[$key] = 'ERROR';
    }
}

// 统计结果
$passed = count(array_filter($results, function($r) { return $r === 'PASS'; }));
$total = count($results);

echo "\n=== 测试结果 ===\n";
echo "通过: {$passed}/{$total}\n";

if ($passed === $total) {
    echo "🎉 所有基础功能正常！\n";
} else {
    echo "⚠️ 有功能异常，请检查详细日志\n";
}

/**
 * 测试数据库连接
 */
function testDatabase()
{
    try {
        // 基础连接测试
        $result = \think\Db::query('SELECT 1 as test');
        if (empty($result)) {
            return false;
        }
        
        // 测试表是否存在
        $tables = [
            'ls_poster_template_configs',
            'ls_poster_user_data',
            'ls_poster_generation_records',
            'ls_poster_api_keys'
        ];
        
        foreach ($tables as $table) {
            $exists = \think\Db::query("SHOW TABLES LIKE '{$table}'");
            if (empty($exists)) {
                echo "\n  表 {$table} 不存在";
                return false;
            }
        }
        
        return true;
    } catch (Exception $e) {
        echo "\n  数据库错误: " . $e->getMessage();
        return false;
    }
}

/**
 * 测试模型功能
 */
function testModels()
{
    try {
        // 测试模型类是否可以加载
        $config = new \app\common\model\PosterTemplateConfig();
        $userData = new \app\common\model\PosterUserData();
        $record = new \app\common\model\PosterGenerationRecord();
        
        // 测试ID生成
        $configId = $config::generateId();
        $dataId = $userData::generateId();
        $recordId = $record::generateId();
        
        // 验证ID格式（允许字母、数字、下划线）
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $configId) ||
            !preg_match('/^[a-zA-Z0-9_]+$/', $dataId) ||
            !preg_match('/^[a-zA-Z0-9_]+$/', $recordId)) {
            echo "\n  ID格式不正确: config={$configId}, data={$dataId}, record={$recordId}";
            return false;
        }
        
        return true;
    } catch (Exception $e) {
        echo "\n  模型错误: " . $e->getMessage();
        return false;
    }
}

/**
 * 测试配置加载
 */
function testConfig()
{
    try {
        // 测试主要配置文件
        $appConfig = \think\facade\Config::get('app');
        $dbConfig = \think\facade\Config::get('database');
        $posterConfig = \think\facade\Config::get('poster');
        
        if (empty($appConfig) || empty($dbConfig)) {
            echo "\n  核心配置加载失败";
            return false;
        }

        if (empty($posterConfig)) {
            echo "\n  海报配置加载失败，但不影响基础功能";
            // 不返回false，因为海报配置可能在某些环境下不可用
        }
        
        return true;
    } catch (Exception $e) {
        echo "\n  配置错误: " . $e->getMessage();
        return false;
    }
}

/**
 * 测试API健康检查
 */
function testApiHealth()
{
    try {
        // 简化的API健康检查，只验证API密钥是否存在
        $apiKey = \think\Db::table('ls_poster_api_keys')
            ->where('api_key', 'poster_api_key_2025_secure_token_12345')
            ->where('is_active', 1)
            ->find();

        if (!$apiKey) {
            echo "\n  API密钥不存在";
            return false;
        }

        // 检查API控制器类是否存在
        if (!class_exists('\app\api\controller\External')) {
            echo "\n  External API控制器不存在";
            return false;
        }

        return true;
    } catch (Exception $e) {
        echo "\n  API错误: " . $e->getMessage();
        return false;
    }
}

echo "\n提示: 如需运行完整测试，请使用: php tests/run_comprehensive_test.php\n";
