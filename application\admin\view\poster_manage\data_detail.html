{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>数据详情 - {$detail.id}</span>
            <div class="layui-btn-group fr">
                <a class="layui-btn layui-btn-sm layui-btn-primary" href="{:url('data_list')}">
                    <i class="layui-icon layui-icon-return"></i> 返回列表
                </a>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <!-- 基本信息 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">基本信息</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr>
                                        <td width="120"><strong>数据ID</strong></td>
                                        <td>{$detail.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>配置ID</strong></td>
                                        <td>
                                            <a href="{:url('config_detail', ['id' => $detail.config_id])}" class="layui-btn layui-btn-xs">
                                                {$detail.config_id}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>配置名称</strong></td>
                                        <td>{$detail.config_info.config_name|default='未知配置'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>模板ID</strong></td>
                                        <td>{$detail.template_id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>用户ID</strong></td>
                                        <td>{$detail.user_id|default='未知用户'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>数据状态</strong></td>
                                        <td>
                                            {if condition="$detail.is_draft == 1"}
                                            <span class="layui-badge layui-bg-orange">草稿</span>
                                            {else/}
                                            <span class="layui-badge layui-bg-green">已提交</span>
                                            {/if}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>参数数量</strong></td>
                                        <td>{$detail.parameter_values_array|count} 个</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间</strong></td>
                                        <td>{$detail.create_time|date='Y-m-d H:i:s',###}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间</strong></td>
                                        <td>{$detail.update_time|date='Y-m-d H:i:s',###}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 生成状态 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">生成状态</div>
                        <div class="layui-card-body">
                            <div id="generationStatus">
                                <div style="text-align: center; padding: 30px; color: #999;">
                                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>
                                    <p>正在检查生成状态...</p>
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 15px;">
                                <button class="layui-btn layui-btn-sm" onclick="checkGenerationStatus()">
                                    <i class="layui-icon layui-icon-refresh"></i> 刷新状态
                                </button>
                                <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="viewResult()">
                                    <i class="layui-icon layui-icon-eye"></i> 查看结果
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 参数值详情 -->
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">参数值详情</div>
                        <div class="layui-card-body">
                            {if condition="$detail.parameter_values_array && count($detail.parameter_values_array) > 0"}
                            <table class="layui-table" lay-size="sm">
                                <thead>
                                    <tr>
                                        <th width="200">参数名称</th>
                                        <th>参数值</th>
                                        <th width="100">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {volist name="detail.parameter_values_array" id="value" key="paramName"}
                                    <tr>
                                        <td><code>{$paramName}</code></td>
                                        <td>
                                            {if condition="$value"}
                                            <div style="max-width: 400px; word-break: break-all;">
                                                {if condition="strlen($value) > 100"}
                                                <span class="param-value-short">{$value|substr=0,100}...</span>
                                                <span class="param-value-full" style="display: none;">{$value}</span>
                                                <a href="javascript:;" onclick="toggleParamValue(this)" class="layui-btn layui-btn-xs layui-btn-primary">展开</a>
                                                {else/}
                                                {$value}
                                                {/if}
                                            </div>
                                            {else/}
                                            <span style="color: #ccc;">空值</span>
                                            {/if}
                                        </td>
                                        <td>
                                            <button class="layui-btn layui-btn-xs" onclick="copyParamValue('{$value}')">
                                                <i class="layui-icon layui-icon-file"></i> 复制
                                            </button>
                                        </td>
                                    </tr>
                                    {/volist}
                                </tbody>
                            </table>
                            {else/}
                            <div style="text-align: center; padding: 50px; color: #999;">
                                <i class="layui-icon layui-icon-face-cry" style="font-size: 48px;"></i>
                                <p>暂无参数值</p>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>

            <!-- JSON数据 -->
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            原始JSON数据
                            <div class="layui-btn-group fr">
                                <button class="layui-btn layui-btn-xs" onclick="copyJsonData()">
                                    <i class="layui-icon layui-icon-file"></i> 复制JSON
                                </button>
                                <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="downloadJsonData()">
                                    <i class="layui-icon layui-icon-download-circle"></i> 下载JSON
                                </button>
                            </div>
                        </div>
                        <div class="layui-card-body">
                            <pre id="jsonData" style="background: #f5f5f5; padding: 15px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">{$detail.parameter_values|json_encode:JSON_PRETTY_PRINT}</pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="layui-row" style="margin-top: 15px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">操作</div>
                        <div class="layui-card-body">
                            <div style="text-align: center; padding: 20px;">
                                <button class="layui-btn" onclick="editData()">
                                    <i class="layui-icon layui-icon-edit"></i> 编辑数据
                                </button>
                                <button class="layui-btn layui-btn-normal" onclick="viewResult()">
                                    <i class="layui-icon layui-icon-eye"></i> 查看结果
                                </button>
                                <button class="layui-btn layui-btn-warm" onclick="regenerate()">
                                    <i class="layui-icon layui-icon-refresh-1"></i> 重新生成
                                </button>
                                <button class="layui-btn layui-btn-primary" onclick="exportData()">
                                    <i class="layui-icon layui-icon-export"></i> 导出数据
                                </button>
                                <button class="layui-btn layui-btn-danger" onclick="deleteData()">
                                    <i class="layui-icon layui-icon-delete"></i> 删除数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 页面加载时检查生成状态
    checkGenerationStatus();

    // 检查生成状态
    window.checkGenerationStatus = function() {
        $('#generationStatus').html(`
            <div style="text-align: center; padding: 30px; color: #999;">
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>
                <p>正在检查生成状态...</p>
            </div>
        `);

        // 这里应该调用生成状态API，暂时显示模拟状态
        setTimeout(function(){
            var statusHtml = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 18px; color: #5FB878; margin-bottom: 10px;">
                        <i class="layui-icon layui-icon-ok-circle"></i> 生成完成
                    </div>
                    <p style="color: #666;">海报已成功生成</p>
                    <p style="font-size: 12px; color: #999;">生成时间: ` + new Date().toLocaleString() + `</p>
                </div>
            `;
            $('#generationStatus').html(statusHtml);
        }, 1000);
    };

    // 查看结果
    window.viewResult = function() {
        var resultUrl = '/user/poster_result.html?data_id={$detail.id}';
        window.open(resultUrl, '_blank');
    };

    // 编辑数据
    window.editData = function() {
        var editUrl = '/user/poster_data_submit.html?config_id={$detail.config_id}&data_id={$detail.id}';
        window.open(editUrl, '_blank');
    };

    // 重新生成
    window.regenerate = function() {
        layer.confirm('确定要重新生成海报吗？', {icon: 3, title:'确认重新生成'}, function(index){
            layer.msg('重新生成功能开发中...', {icon: 1});
            layer.close(index);
        });
    };

    // 导出数据
    window.exportData = function() {
        var data = {
            id: '{$detail.id}',
            config_id: '{$detail.config_id}',
            template_id: '{$detail.template_id}',
            user_id: '{$detail.user_id}',
            parameter_values: {$detail.parameter_values|raw},
            is_draft: {$detail.is_draft},
            create_time: '{$detail.create_time|date="Y-m-d H:i:s",###}',
            update_time: '{$detail.update_time|date="Y-m-d H:i:s",###}'
        };

        var blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = 'poster_data_{$detail.id}.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        layer.msg('导出成功', {icon: 1});
    };

    // 删除数据
    window.deleteData = function() {
        layer.confirm('确定要删除这条数据吗？删除后无法恢复！', {icon: 3, title:'确认删除'}, function(index){
            $.post('{:url("del_data")}', {id: '{$detail.id}'}, function(res){
                if(res.code == 1){
                    layer.msg('删除成功', {icon: 1}, function(){
                        location.href = '{:url("data_list")}';
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    };

    // 切换参数值显示
    window.toggleParamValue = function(btn) {
        var $btn = $(btn);
        var $tr = $btn.closest('tr');
        var $short = $tr.find('.param-value-short');
        var $full = $tr.find('.param-value-full');
        
        if ($short.is(':visible')) {
            $short.hide();
            $full.show();
            $btn.text('收起');
        } else {
            $short.show();
            $full.hide();
            $btn.text('展开');
        }
    };

    // 复制参数值
    window.copyParamValue = function(value) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(value).then(function() {
                layer.msg('已复制到剪贴板', {icon: 1});
            });
        } else {
            var textArea = document.createElement('textarea');
            textArea.value = value;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            layer.msg('已复制到剪贴板', {icon: 1});
        }
    };

    // 复制JSON数据
    window.copyJsonData = function() {
        var jsonText = document.getElementById('jsonData').textContent;
        if (navigator.clipboard) {
            navigator.clipboard.writeText(jsonText).then(function() {
                layer.msg('JSON数据已复制到剪贴板', {icon: 1});
            });
        } else {
            var textArea = document.createElement('textarea');
            textArea.value = jsonText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            layer.msg('JSON数据已复制到剪贴板', {icon: 1});
        }
    };

    // 下载JSON数据
    window.downloadJsonData = function() {
        var jsonText = document.getElementById('jsonData').textContent;
        var blob = new Blob([jsonText], {type: 'application/json'});
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = 'poster_data_{$detail.id}_parameters.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        layer.msg('下载成功', {icon: 1});
    };
});
</script>
</div>
