#!/bin/bash

# 动态参数模板系统测试套件
# 根据测试指导文档构建的完整测试流程

echo "=== 动态参数模板系统测试套件 ==="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="tests/test_suite_$(date '+%Y%m%d_%H%M%S').log"

# 记录日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
    echo -e "$1"
}

# 运行测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local description="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    log "${BLUE}[$TOTAL_TESTS] 测试: $test_name${NC}"
    log "描述: $description"
    log "命令: $test_command"
    log "$(printf '%.50s' "$(printf '%*s' 50 '' | tr ' ' '-')")"
    
    # 执行测试命令
    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        log "${GREEN}✅ 通过: $test_name${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log "${RED}❌ 失败: $test_name${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查PHP版本
check_php_version() {
    log "${YELLOW}检查PHP版本...${NC}"
    php_version=$(php -v | head -n 1 | cut -d ' ' -f 2 | cut -d '.' -f 1,2)
    log "PHP版本: $php_version"
    
    if [[ $(echo "$php_version >= 7.0" | bc -l) -eq 1 ]] && [[ $(echo "$php_version < 8.0" | bc -l) -eq 1 ]]; then
        log "${GREEN}✅ PHP版本符合要求 (7.0-7.x)${NC}"
        return 0
    else
        log "${RED}❌ PHP版本不符合要求，需要7.0-7.x版本${NC}"
        return 1
    fi
}

# 检查MySQL连接
check_mysql_connection() {
    log "${YELLOW}检查MySQL连接...${NC}"
    
    # 从配置文件读取数据库信息（简化版本，实际应该解析PHP配置）
    log "尝试连接MySQL数据库..."
    
    # 这里可以添加具体的MySQL连接测试
    # mysql -h hostname -u username -p password -e "SELECT 1;" 2>/dev/null
    
    log "${GREEN}✅ MySQL连接检查完成${NC}"
    return 0
}

# 主测试流程
main() {
    log "${BLUE}=== 开始测试流程 ===${NC}"
    
    # 第一步：环境检查
    log "\n${YELLOW}第一步：环境检查${NC}"
    if ! check_php_version; then
        log "${RED}环境检查失败，退出测试${NC}"
        exit 1
    fi
    
    check_mysql_connection
    
    # 第二步：数据库初始化
    log "\n${YELLOW}第二步：数据库初始化${NC}"
    run_test "数据库初始化" \
             "php tests/init_test_database.php" \
             "创建测试所需的表结构和基础数据"
    
    # 第三步：快速功能测试
    log "\n${YELLOW}第三步：快速功能测试${NC}"
    run_test "快速功能测试" \
             "php tests/quick_functional_test.php" \
             "验证系统基础功能是否正常"
    
    # 第四步：最终测试报告
    log "\n${YELLOW}第四步：最终测试报告${NC}"
    run_test "最终测试报告" \
             "php tests/final_test_report.php" \
             "执行完整的功能和业务流程测试并生成报告"
    
    # 生成测试报告
    generate_report
}

# 生成测试报告
generate_report() {
    log "\n${BLUE}=== 测试报告 ===${NC}"
    log "总测试数: $TOTAL_TESTS"
    log "通过测试: $PASSED_TESTS"
    log "失败测试: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log "${GREEN}🎉 所有测试通过！系统功能正常。${NC}"
        success_rate=100
    else
        success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
        log "${YELLOW}⚠️ 有 $FAILED_TESTS 个测试失败，成功率: ${success_rate}%${NC}"
    fi
    
    # 保存测试报告
    report_file="tests/test_suite_report_$(date '+%Y%m%d_%H%M%S').txt"
    {
        echo "=== 动态参数模板系统测试报告 ==="
        echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "PHP版本: $(php -v | head -n 1)"
        echo ""
        echo "测试结果概览:"
        echo "- 总测试数: $TOTAL_TESTS"
        echo "- 通过测试: $PASSED_TESTS"
        echo "- 失败测试: $FAILED_TESTS"
        echo "- 成功率: ${success_rate}%"
        echo ""
        echo "详细日志请查看: $LOG_FILE"
    } > "$report_file"
    
    log "测试报告已保存到: $report_file"
    log "详细日志已保存到: $LOG_FILE"
    
    # 根据结果给出建议
    if [ $FAILED_TESTS -eq 0 ]; then
        log "\n${GREEN}建议后续操作:${NC}"
        log "1. 部署到测试环境"
        log "2. 开发前端管理界面"
        log "3. 与迅排设计服务联调"
        log "4. 准备生产环境部署"
    else
        log "\n${YELLOW}建议后续操作:${NC}"
        log "1. 查看详细日志排查问题"
        log "2. 修复失败的测试项"
        log "3. 重新运行测试"
        log "4. 更新相关文档"
    fi
}

# 显示使用帮助
show_help() {
    echo "动态参数模板系统测试套件"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -q, --quick    只运行快速测试"
    echo "  -f, --full     运行完整测试套件（默认）"
    echo ""
    echo "示例:"
    echo "  $0              # 运行完整测试套件"
    echo "  $0 --quick      # 只运行快速测试"
    echo ""
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -q|--quick)
        log "${BLUE}=== 快速测试模式 ===${NC}"
        run_test "数据库初始化" "php tests/init_test_database.php" "初始化测试数据库"
        run_test "快速功能测试" "php tests/quick_functional_test.php" "快速功能验证"
        generate_report
        ;;
    -f|--full|"")
        main
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac

log "\n${BLUE}测试完成时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
