-- 动态参数模板系统数据库表设计
-- 基于 ThinkPHP 5.1 和 MySQL 数据库

-- 1. 模板参数配置表
CREATE TABLE `ls_poster_template_configs` (
  `id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` varchar(255) DEFAULT '' COMMENT '模板标题',
  `config_name` varchar(255) NOT NULL COMMENT '配置名称',
  `config_description` text COMMENT '配置描述',
  `parameters` json NOT NULL COMMENT '参数定义JSON',
  `created_by` int(11) DEFAULT 0 COMMENT '创建者ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报模板参数配置表';

-- 2. 用户参数数据表
CREATE TABLE `ls_poster_user_data` (
  `id` varchar(32) NOT NULL COMMENT '数据ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `user_id` int(11) DEFAULT 0 COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT '' COMMENT '会话ID（匿名用户）',
  `parameter_values` json NOT NULL COMMENT '用户填写的参数值',
  `is_draft` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为草稿：1草稿 0正式',
  `preview_url` varchar(500) DEFAULT '' COMMENT '预览页面URL',
  `generated_image_url` varchar(500) DEFAULT '' COMMENT '生成的图片URL',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_is_draft` (`is_draft`),
  CONSTRAINT `fk_poster_user_data_config` FOREIGN KEY (`config_id`) REFERENCES `ls_poster_template_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报用户参数数据表';

-- 3. 图片生成记录表
CREATE TABLE `ls_poster_generation_records` (
  `id` varchar(32) NOT NULL COMMENT '记录ID',
  `data_id` varchar(32) NOT NULL COMMENT '参数数据ID',
  `image_url` varchar(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` json DEFAULT NULL COMMENT '生成选项',
  `generation_time` decimal(10,3) DEFAULT NULL COMMENT '生成耗时（秒）',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小（字节）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_data_id` (`data_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_poster_generation_data` FOREIGN KEY (`data_id`) REFERENCES `ls_poster_user_data` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报图片生成记录表';

-- 4. API密钥管理表
CREATE TABLE `ls_poster_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `key_name` varchar(100) NOT NULL COMMENT '密钥名称',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `permissions` json DEFAULT NULL COMMENT '权限配置',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：1激活 0禁用',
  `last_used_time` int(11) DEFAULT NULL COMMENT '最后使用时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报API密钥管理表';

-- 插入默认API密钥
INSERT INTO `ls_poster_api_keys` (`key_name`, `api_key`, `permissions`, `is_active`, `create_time`, `update_time`) 
VALUES ('迅排设计服务', 'poster_api_key_2025_secure_token_12345', '["parameter_data", "parameter_config", "health_check"]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
