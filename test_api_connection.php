<?php
/**
 * 简单的API连接测试
 */

// 测试基本的HTTP连接
$baseUrl = 'http://localhost:7001';
$testUrl = $baseUrl . '/api/external/templates?page=1&pageSize=5';

echo "测试API连接...\n";
echo "测试URL: {$testUrl}\n\n";

// 使用cURL测试连接
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $testUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json',
    ],
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "CURL错误: {$error}\n";
} else {
    echo "响应内容: " . substr($response, 0, 200) . "...\n";
    
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "JSON解析成功\n";
        echo "响应代码: " . ($data['code'] ?? 'N/A') . "\n";
        echo "响应消息: " . ($data['msg'] ?? 'N/A') . "\n";
    } else {
        echo "JSON解析失败: " . json_last_error_msg() . "\n";
    }
}

echo "\n测试完成\n";
