# 外部项目集成示例

## 概述

本文档提供了外部项目如何调用迅排设计模板接口的完整示例，包括跨域访问、数据获取和错误处理。

## 接口信息

### 基础信息
- **服务地址**: `http://localhost:7001` (开发环境)
- **接口路径**: `/api/external/templates` (推荐) 或 `/design/list` (兼容)
- **请求方法**: GET
- **跨域支持**: ✅ 已配置CORS，支持所有来源访问

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| search | String | 否 | '' | 搜索关键词，支持模板标题搜索 |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 20 | 每页数量，建议10-50 |
| cate | String | 否 | - | 分类ID |
| type | Number | 否 | 0 | 类型：0=模板，1=组件 |

### 响应格式
```json
{
  "code": 200,
  "msg": "ok",
  "result": {
    "list": [
      {
        "id": 1,
        "title": "示例模板1",
        "cover": "https://pic.imgdb.cn/item/66b93ff9d9c307b7e92cbb2b.jpg",
        "thumbnail": "https://pic.imgdb.cn/item/66b93ff9d9c307b7e92cbb2b.jpg",
        "width": 1242,
        "height": 2208,
        "state": 1
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 2,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

## 集成示例

### 1. JavaScript/Fetch 示例

```javascript
// 基础调用示例
async function getTemplateList(options = {}) {
  const {
    search = '',
    page = 1,
    pageSize = 20,
    cate = '',
    type = 0
  } = options;

  const params = new URLSearchParams({
    search,
    page: page.toString(),
    pageSize: pageSize.toString(),
    ...(cate && { cate }),
    type: type.toString()
  });

  try {
    const response = await fetch(`http://localhost:7001/api/external/templates?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.code === 200) {
      return {
        success: true,
        templates: data.result.list,
        pagination: data.result.pagination
      };
    } else {
      throw new Error(data.msg || '获取模板列表失败');
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    return {
      success: false,
      error: error.message,
      templates: [],
      pagination: null
    };
  }
}

// 使用示例
async function loadTemplates() {
  const result = await getTemplateList({
    search: '海报',
    page: 1,
    pageSize: 10
  });

  if (result.success) {
    console.log('模板列表:', result.templates);
    console.log('分页信息:', result.pagination);
    
    // 渲染模板列表
    renderTemplateList(result.templates, result.pagination);
  } else {
    console.error('加载失败:', result.error);
    showErrorMessage(result.error);
  }
}

// 渲染模板列表示例
function renderTemplateList(templates, pagination) {
  const container = document.getElementById('template-list');
  
  // 清空容器
  container.innerHTML = '';
  
  // 渲染模板项
  templates.forEach(template => {
    const templateElement = document.createElement('div');
    templateElement.className = 'template-item';
    templateElement.innerHTML = `
      <div class="template-card">
        <img src="${template.thumbnail}" alt="${template.title}" 
             onerror="this.src='placeholder.jpg'">
        <h3>${template.title}</h3>
        <p>尺寸: ${template.width} x ${template.height}</p>
        <button onclick="selectTemplate(${template.id})">选择模板</button>
      </div>
    `;
    container.appendChild(templateElement);
  });
  
  // 渲染分页
  renderPagination(pagination);
}

// 分页渲染示例
function renderPagination(pagination) {
  const paginationContainer = document.getElementById('pagination');
  paginationContainer.innerHTML = `
    <div class="pagination">
      <button ${!pagination.hasPrev ? 'disabled' : ''} 
              onclick="loadPage(${pagination.page - 1})">上一页</button>
      <span>第 ${pagination.page} 页，共 ${pagination.totalPages} 页</span>
      <button ${!pagination.hasNext ? 'disabled' : ''} 
              onclick="loadPage(${pagination.page + 1})">下一页</button>
      <span>共 ${pagination.total} 个模板</span>
    </div>
  `;
}
```

### 2. Vue.js 示例

```vue
<template>
  <div class="template-manager">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <input 
        v-model="searchQuery" 
        @input="debounceSearch"
        placeholder="搜索模板..."
        class="search-input"
      >
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">加载中...</div>

    <!-- 错误信息 -->
    <div v-if="error" class="error">{{ error }}</div>

    <!-- 模板列表 -->
    <div v-if="!loading && !error" class="template-grid">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-card"
        @click="selectTemplate(template)"
      >
        <img 
          :src="template.thumbnail" 
          :alt="template.title"
          @error="handleImageError"
        >
        <h3>{{ template.title }}</h3>
        <p>{{ template.width }} x {{ template.height }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination && pagination.totalPages > 1" class="pagination">
      <button 
        :disabled="!pagination.hasPrev"
        @click="loadPage(pagination.page - 1)"
      >
        上一页
      </button>
      <span>{{ pagination.page }} / {{ pagination.totalPages }}</span>
      <button 
        :disabled="!pagination.hasNext"
        @click="loadPage(pagination.page + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'TemplateManager',
  setup() {
    const templates = ref([])
    const pagination = ref(null)
    const loading = ref(false)
    const error = ref('')
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)

    // 获取模板列表
    const fetchTemplates = async (options = {}) => {
      loading.value = true
      error.value = ''

      try {
        const params = new URLSearchParams({
          search: searchQuery.value,
          page: currentPage.value.toString(),
          pageSize: pageSize.value.toString(),
          ...options
        })

        const response = await fetch(`http://localhost:7001/api/external/templates?${params}`)
        const data = await response.json()

        if (data.code === 200) {
          templates.value = data.result.list
          pagination.value = data.result.pagination
        } else {
          throw new Error(data.msg || '获取模板失败')
        }
      } catch (err) {
        error.value = err.message
        templates.value = []
        pagination.value = null
      } finally {
        loading.value = false
      }
    }

    // 搜索防抖
    let searchTimeout
    const debounceSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        currentPage.value = 1
        fetchTemplates()
      }, 500)
    }

    // 加载指定页
    const loadPage = (page) => {
      currentPage.value = page
      fetchTemplates()
    }

    // 选择模板
    const selectTemplate = (template) => {
      console.log('选择模板:', template)
      // 这里可以触发事件或调用父组件方法
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = 'placeholder.jpg'
    }

    // 初始化
    onMounted(() => {
      fetchTemplates()
    })

    return {
      templates,
      pagination,
      loading,
      error,
      searchQuery,
      debounceSearch,
      loadPage,
      selectTemplate,
      handleImageError
    }
  }
}
</script>

<style scoped>
.template-manager {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: transform 0.2s;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.template-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading, .error {
  text-align: center;
  padding: 20px;
}

.error {
  color: red;
}
</style>
```

## 注意事项

### 1. 跨域配置
- 服务已配置CORS，支持所有来源访问
- 如需限制访问来源，可修改 `service/src/main.ts` 中的CORS配置

### 2. 错误处理
- 始终检查响应状态码和业务状态码
- 实现图片加载失败的降级处理
- 提供用户友好的错误提示

### 3. 性能优化
- 实现搜索防抖，避免频繁请求
- 合理设置分页大小，建议10-50条
- 考虑实现缓存机制

### 4. 安全考虑
- 在生产环境中使用HTTPS
- 考虑添加API访问频率限制
- 验证和清理用户输入

## 故障排查

### 常见问题
1. **跨域错误**: 确认服务已启动且CORS配置正确
2. **接口404**: 检查接口路径是否正确
3. **数据为空**: 检查模板数据文件是否存在
4. **图片无法显示**: 检查图片URL是否可访问

### 调试建议
- 使用浏览器开发者工具查看网络请求
- 检查控制台错误信息
- 验证请求参数格式是否正确

## 技术支持

如有问题，请参考：
- [API接口文档](./API.md)
- [主项目集成指南](./主项目集成指南.md)
- 项目GitHub Issues
