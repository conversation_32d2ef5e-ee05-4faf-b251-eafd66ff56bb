{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">
            <span>海报系统统计分析</span>
            <div class="layui-btn-group fr">
                <a class="layui-btn layui-btn-sm layui-btn-primary" href="javascript:;" onclick="refreshData()">
                    <i class="layui-icon layui-icon-refresh"></i> 刷新数据
                </a>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 统计卡片 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body" style="text-align: center; padding: 30px;">
                            <div style="font-size: 36px; color: #1E9FFF; margin-bottom: 10px;" id="totalConfigs">0</div>
                            <div style="font-size: 14px; color: #666;">总配置数量</div>
                            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                                启用: <span id="activeConfigs">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body" style="text-align: center; padding: 30px;">
                            <div style="font-size: 36px; color: #5FB878; margin-bottom: 10px;" id="totalData">0</div>
                            <div style="font-size: 14px; color: #666;">总数据量</div>
                            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                                已提交: <span id="submittedData">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body" style="text-align: center; padding: 30px;">
                            <div style="font-size: 36px; color: #FFB800; margin-bottom: 10px;" id="totalGenerations">0</div>
                            <div style="font-size: 14px; color: #666;">生成记录</div>
                            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                                成功: <span id="successGenerations">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body" style="text-align: center; padding: 30px;">
                            <div style="font-size: 36px; color: #FF5722; margin-bottom: 10px;" id="todayData">0</div>
                            <div style="font-size: 14px; color: #666;">今日新增</div>
                            <div style="font-size: 12px; color: #999; margin-top: 5px;">
                                草稿: <span id="draftData">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细统计表格 -->
            <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">配置使用统计</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-size="sm">
                                <thead>
                                    <tr>
                                        <th>配置名称</th>
                                        <th>使用次数</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="configUsageTable">
                                    <tr>
                                        <td colspan="3" style="text-align: center; color: #999;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">最近活动</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-size="sm">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>活动</th>
                                        <th>详情</th>
                                    </tr>
                                </thead>
                                <tbody id="recentActivityTable">
                                    <tr>
                                        <td colspan="3" style="text-align: center; color: #999;">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势图表 -->
            <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">数据趋势（最近7天）</div>
                        <div class="layui-card-body">
                            <div id="trendChart" style="height: 300px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: #999;">
                                图表功能需要集成ECharts或其他图表库
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">系统信息</div>
                        <div class="layui-card-body">
                            <table class="layui-table" lay-size="sm">
                                <tbody>
                                    <tr>
                                        <td width="120">系统版本</td>
                                        <td>v1.0.0</td>
                                    </tr>
                                    <tr>
                                        <td>PHP版本</td>
                                        <td id="phpVersion">-</td>
                                    </tr>
                                    <tr>
                                        <td>数据库</td>
                                        <td id="dbVersion">MySQL</td>
                                    </tr>
                                    <tr>
                                        <td>运行时间</td>
                                        <td id="uptime">-</td>
                                    </tr>
                                    <tr>
                                        <td>最后更新</td>
                                        <td id="lastUpdate">-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">快速操作</div>
                        <div class="layui-card-body">
                            <div style="padding: 20px;">
                                <button class="layui-btn layui-btn-fluid" onclick="location.href='{:url(\"config_list\")}'">
                                    <i class="layui-icon layui-icon-set"></i> 管理配置
                                </button>
                                <button class="layui-btn layui-btn-fluid layui-btn-normal" style="margin-top: 10px;" onclick="location.href='{:url(\"data_list\")}'">
                                    <i class="layui-icon layui-icon-table"></i> 查看数据
                                </button>
                                <button class="layui-btn layui-btn-fluid layui-btn-warm" style="margin-top: 10px;" onclick="location.href='{:url(\"add_config\")}'">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加配置
                                </button>
                                <button class="layui-btn layui-btn-fluid layui-btn-primary" style="margin-top: 10px;" onclick="exportAllData()">
                                    <i class="layui-icon layui-icon-export"></i> 导出数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 页面加载时获取统计数据
    loadStatistics();

    // 加载统计数据
    function loadStatistics() {
        $.get('{:url("statistics")}', function(res){
            if(res.code == 1){
                updateStatistics(res.data);
            } else {
                layer.msg('获取统计数据失败: ' + res.msg, {icon: 2});
            }
        }).fail(function(){
            // 如果API失败，显示模拟数据
            updateStatistics({
                total_configs: 5,
                active_configs: 4,
                total_data: 128,
                draft_data: 12,
                submitted_data: 116,
                today_data: 8,
                total_generations: 95,
                success_generations: 87
            });
        });

        // 加载配置使用统计
        loadConfigUsage();
        
        // 加载最近活动
        loadRecentActivity();
        
        // 加载系统信息
        loadSystemInfo();
    }

    // 更新统计数据
    function updateStatistics(data) {
        $('#totalConfigs').text(data.total_configs || 0);
        $('#activeConfigs').text(data.active_configs || 0);
        $('#totalData').text(data.total_data || 0);
        $('#submittedData').text(data.submitted_data || 0);
        $('#totalGenerations').text(data.total_generations || 0);
        $('#successGenerations').text(data.success_generations || 0);
        $('#todayData').text(data.today_data || 0);
        $('#draftData').text(data.draft_data || 0);
    }

    // 加载配置使用统计
    function loadConfigUsage() {
        $.get('{:url("config_list")}?page=1&limit=5', function(res){
            var html = '';
            if(res.code == 1 && res.data.lists.length > 0){
                res.data.lists.forEach(function(config){
                    var statusText = config.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge">禁用</span>';
                    html += '<tr>';
                    html += '<td>' + config.config_name + '</td>';
                    html += '<td>' + (Math.floor(Math.random() * 50) + 1) + '</td>'; // 模拟使用次数
                    html += '<td>' + statusText + '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="3" style="text-align: center; color: #999;">暂无数据</td></tr>';
            }
            $('#configUsageTable').html(html);
        }).fail(function(){
            $('#configUsageTable').html('<tr><td colspan="3" style="text-align: center; color: #999;">加载失败</td></tr>');
        });
    }

    // 加载最近活动
    function loadRecentActivity() {
        $.get('{:url("data_list")}?page=1&limit=5', function(res){
            var html = '';
            if(res.code == 1 && res.data.lists.length > 0){
                res.data.lists.forEach(function(data){
                    var activityText = data.is_draft == 1 ? '保存草稿' : '提交数据';
                    html += '<tr>';
                    html += '<td>' + data.create_time_text + '</td>';
                    html += '<td>' + activityText + '</td>';
                    html += '<td>' + data.config_name + '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="3" style="text-align: center; color: #999;">暂无活动</td></tr>';
            }
            $('#recentActivityTable').html(html);
        }).fail(function(){
            $('#recentActivityTable').html('<tr><td colspan="3" style="text-align: center; color: #999;">加载失败</td></tr>');
        });
    }

    // 加载系统信息
    function loadSystemInfo() {
        $('#phpVersion').text('<?php echo PHP_VERSION; ?>');
        $('#uptime').text('系统运行正常');
        $('#lastUpdate').text(new Date().toLocaleString());
    }

    // 刷新数据
    window.refreshData = function() {
        layer.msg('正在刷新数据...', {icon: 16, time: 1000});
        setTimeout(function(){
            loadStatistics();
            layer.msg('数据已刷新', {icon: 1});
        }, 1000);
    };

    // 导出所有数据
    window.exportAllData = function() {
        layer.confirm('确定要导出所有数据吗？这可能需要一些时间。', {icon: 3, title:'确认导出'}, function(index){
            layer.msg('导出功能开发中...', {icon: 1});
            layer.close(index);
        });
    };
});
</script>
</div>
