@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 动态参数模板系统测试套件 (Windows版本)
:: 根据测试指导文档构建的完整测试流程

echo === 动态参数模板系统测试套件 ===
echo 开始时间: %date% %time%
echo.

:: 测试结果统计
set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

:: 日志文件
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
set LOG_FILE=tests\test_suite_%mydate%_%mytime%.log

:: 创建日志文件
echo 测试开始时间: %date% %time% > "%LOG_FILE%"

:: 记录日志函数
:log
echo %date% %time% - %~1 >> "%LOG_FILE%"
echo %~1
goto :eof

:: 运行测试函数
:run_test
set /a TOTAL_TESTS+=1
set test_name=%~1
set test_command=%~2
set description=%~3

call :log "[%TOTAL_TESTS%] 测试: %test_name%"
call :log "描述: %description%"
call :log "命令: %test_command%"
call :log "------------------------------------------------"

:: 执行测试命令
%test_command% >> "%LOG_FILE%" 2>&1
if !errorlevel! equ 0 (
    call :log "✅ 通过: %test_name%"
    set /a PASSED_TESTS+=1
    set test_result=PASS
) else (
    call :log "❌ 失败: %test_name%"
    set /a FAILED_TESTS+=1
    set test_result=FAIL
)
goto :eof

:: 检查PHP版本
:check_php_version
call :log "检查PHP版本..."
php -v > temp_php_version.txt 2>&1
if !errorlevel! neq 0 (
    call :log "❌ PHP未安装或不在PATH中"
    del temp_php_version.txt 2>nul
    exit /b 1
)

for /f "tokens=2" %%a in ('php -v ^| findstr "PHP"') do (
    set php_version=%%a
    goto :php_version_found
)

:php_version_found
call :log "PHP版本: !php_version!"
del temp_php_version.txt 2>nul

:: 简单的版本检查（检查是否以7开头）
echo !php_version! | findstr "^7\." >nul
if !errorlevel! equ 0 (
    call :log "✅ PHP版本符合要求 (7.x)"
    exit /b 0
) else (
    call :log "❌ PHP版本不符合要求，需要7.0-7.x版本"
    exit /b 1
)

:: 主测试流程
:main
call :log "=== 开始测试流程 ==="

:: 第一步：环境检查
call :log ""
call :log "第一步：环境检查"
call :check_php_version
if !errorlevel! neq 0 (
    call :log "环境检查失败，退出测试"
    goto :generate_report
)

:: 第二步：数据库初始化
call :log ""
call :log "第二步：数据库初始化"
call :run_test "数据库初始化" "php tests\init_test_database.php" "创建测试所需的表结构和基础数据"

:: 第三步：快速功能测试
call :log ""
call :log "第三步：快速功能测试"
call :run_test "快速功能测试" "php tests\quick_functional_test.php" "验证系统基础功能是否正常"

:: 第四步：最终测试报告
call :log ""
call :log "第四步：最终测试报告"
call :run_test "最终测试报告" "php tests\final_test_report.php" "执行完整的功能和业务流程测试并生成报告"

goto :generate_report

:: 快速测试模式
:quick_test
call :log "=== 快速测试模式 ==="
call :run_test "数据库初始化" "php tests\init_test_database.php" "初始化测试数据库"
call :run_test "快速功能测试" "php tests\quick_functional_test.php" "快速功能验证"
goto :generate_report

:: 生成测试报告
:generate_report
call :log ""
call :log "=== 测试报告 ==="
call :log "总测试数: %TOTAL_TESTS%"
call :log "通过测试: %PASSED_TESTS%"
call :log "失败测试: %FAILED_TESTS%"

if %FAILED_TESTS% equ 0 (
    call :log "🎉 所有测试通过！系统功能正常。"
    set /a success_rate=100
) else (
    set /a success_rate=%PASSED_TESTS%*100/%TOTAL_TESTS%
    call :log "⚠️ 有 %FAILED_TESTS% 个测试失败，成功率: !success_rate!%%"
)

:: 保存测试报告
set report_file=tests\test_suite_report_%mydate%_%mytime%.txt
(
    echo === 动态参数模板系统测试报告 ===
    echo 测试时间: %date% %time%
    php -v | findstr "PHP"
    echo.
    echo 测试结果概览:
    echo - 总测试数: %TOTAL_TESTS%
    echo - 通过测试: %PASSED_TESTS%
    echo - 失败测试: %FAILED_TESTS%
    echo - 成功率: !success_rate!%%
    echo.
    echo 详细日志请查看: %LOG_FILE%
) > "%report_file%"

call :log "测试报告已保存到: %report_file%"
call :log "详细日志已保存到: %LOG_FILE%"

:: 根据结果给出建议
if %FAILED_TESTS% equ 0 (
    call :log ""
    call :log "建议后续操作:"
    call :log "1. 部署到测试环境"
    call :log "2. 开发前端管理界面"
    call :log "3. 与迅排设计服务联调"
    call :log "4. 准备生产环境部署"
) else (
    call :log ""
    call :log "建议后续操作:"
    call :log "1. 查看详细日志排查问题"
    call :log "2. 修复失败的测试项"
    call :log "3. 重新运行测试"
    call :log "4. 更新相关文档"
)

call :log ""
call :log "测试完成时间: %date% %time%"
goto :end

:: 显示使用帮助
:show_help
echo 动态参数模板系统测试套件
echo.
echo 用法: %0 [选项]
echo.
echo 选项:
echo   /h, /help      显示此帮助信息
echo   /q, /quick     只运行快速测试
echo   /f, /full      运行完整测试套件（默认）
echo.
echo 示例:
echo   %0              # 运行完整测试套件
echo   %0 /quick       # 只运行快速测试
echo.
goto :end

:: 处理命令行参数
if "%1"=="/h" goto :show_help
if "%1"=="/help" goto :show_help
if "%1"=="/q" goto :quick_test
if "%1"=="/quick" goto :quick_test
if "%1"=="/f" goto :main
if "%1"=="/full" goto :main
if "%1"=="" goto :main

echo 未知选项: %1
call :show_help

:end
pause
