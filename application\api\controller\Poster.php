<?php

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\server\PosterServer;
use app\api\controller\BaseApi;

/**
 * 海报管理API控制器
 * Class Poster
 * @package app\api\controller
 */
class Poster extends BaseApi
{
    private $posterServer;

    public function initialize()
    {
        parent::initialize();
        $this->posterServer = new PosterServer();
    }

    /**
     * 解析模板
     * POST /api/poster/parse-template
     */
    public function parseTemplate()
    {
        $templateId = $this->request->post('template_id', '');
        
        if (empty($templateId)) {
            $this->error('模板ID不能为空');
        }

        $result = $this->posterServer->parseTemplateAndCreateConfig($templateId, []);
        
        if ($result['code'] === 200) {
            $this->success($result['data'], '模板解析成功');
        } else {
            $this->error($result['message']);
        }
    }

    /**
     * 创建模板配置
     * POST /api/poster/create-config
     */
    public function createConfig()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = $this->validate($params, [
            'template_id|模板ID' => 'require',
            'config_name|配置名称' => 'require|max:255',
            'selected_parameters|选择的参数' => 'require|array',
        ]);

        if ($validate !== true) {
            $this->error($validate);
        }

        // 获取当前用户ID
        $params['created_by'] = $this->getUserId();

        $result = $this->posterServer->parseTemplateAndCreateConfig($params['template_id'], $params);
        
        if ($result['code'] === 200) {
            $this->success($result['data'], '配置创建成功');
        } else {
            $this->error($result['message']);
        }
    }

    /**
     * 获取配置列表
     * GET /api/poster/config-list
     */
    public function getConfigList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $templateId = $this->request->get('template_id', '');
        $keyword = $this->request->get('keyword', '');

        $where = [];
        if (!empty($templateId)) {
            $where['template_id'] = $templateId;
        }
        if (!empty($keyword)) {
            $where['keyword'] = $keyword;
        }

        $result = PosterTemplateConfig::getEnabledList($where, $page, $limit);
        $this->success($result);
    }

    /**
     * 获取配置详情
     * GET /api/poster/config-detail/:id
     */
    public function getConfigDetail($id)
    {
        $config = PosterTemplateConfig::find($id);
        if (!$config) {
            $this->error('配置不存在');
        }

        $this->success($config->toArray());
    }

    /**
     * 更新配置
     * PUT /api/poster/update-config/:id
     */
    public function updateConfig($id)
    {
        $config = PosterTemplateConfig::find($id);
        if (!$config) {
            $this->error('配置不存在');
        }

        $params = $this->request->put();
        
        // 允许更新的字段
        $allowFields = ['config_name', 'config_description', 'parameters', 'status'];
        foreach ($allowFields as $field) {
            if (isset($params[$field])) {
                $config->$field = $params[$field];
            }
        }

        if ($config->save()) {
            $this->success([], '更新成功');
        } else {
            $this->error('更新失败');
        }
    }

    /**
     * 删除配置
     * DELETE /api/poster/delete-config/:id
     */
    public function deleteConfig($id)
    {
        $config = PosterTemplateConfig::find($id);
        if (!$config) {
            $this->error('配置不存在');
        }

        if ($config->delete()) {
            $this->success([], '删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 提交用户数据
     * POST /api/poster/submit-data
     */
    public function submitData()
    {
        $params = $this->request->post();
        
        // 参数验证
        $validate = $this->validate($params, [
            'config_id|配置ID' => 'require',
            'parameter_values|参数值' => 'require|array',
        ]);

        if ($validate !== true) {
            $this->error($validate);
        }

        // 设置用户信息
        $params['user_id'] = $this->getUserId();
        if (empty($params['user_id'])) {
            $params['session_id'] = session_id();
        }

        $result = $this->posterServer->createUserData($params);
        
        if ($result['code'] === 200) {
            $this->success($result['data'], '数据提交成功');
        } else {
            $this->error($result['message'], $result['data']);
        }
    }

    /**
     * 获取用户数据列表
     * GET /api/poster/user-data-list
     */
    public function getUserDataList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $configId = $this->request->get('config_id', '');
        $userId = $this->getUserId();

        $where = [];
        if (!empty($configId)) {
            $where['config_id'] = $configId;
        }
        if (!empty($userId)) {
            $where['user_id'] = $userId;
        }

        $result = PosterUserData::getList($where, $page, $limit);
        $this->success($result);
    }

    /**
     * 获取用户数据详情
     * GET /api/poster/user-data-detail/:id
     */
    public function getUserDataDetail($id)
    {
        $userData = PosterUserData::with(['config'])->find($id);
        if (!$userData) {
            $this->error('数据不存在');
        }

        $this->success($userData->toArray());
    }

    /**
     * 更新用户数据
     * PUT /api/poster/update-user-data/:id
     */
    public function updateUserData($id)
    {
        $userData = PosterUserData::find($id);
        if (!$userData) {
            $this->error('数据不存在');
        }

        $params = $this->request->put();
        
        $result = PosterUserData::updateData($id, $params);
        
        if ($result) {
            $this->success([], '更新成功');
        } else {
            $this->error('更新失败');
        }
    }

    /**
     * 生成预览
     * POST /api/poster/generate-preview
     */
    public function generatePreview()
    {
        $dataId = $this->request->post('data_id', '');
        
        if (empty($dataId)) {
            $this->error('数据ID不能为空');
        }

        $result = $this->posterServer->generatePreview($dataId);
        
        if ($result['code'] === 200) {
            $this->success($result['data'], '预览生成成功');
        } else {
            $this->error($result['message']);
        }
    }

    /**
     * 生成图片
     * POST /api/poster/generate-image
     */
    public function generateImage()
    {
        $dataId = $this->request->post('data_id', '');
        $options = $this->request->post('options', []);
        
        if (empty($dataId)) {
            $this->error('数据ID不能为空');
        }

        // 默认生成选项
        $defaultOptions = [
            'width' => 800,
            'height' => 600,
            'type' => 'file',
            'size' => 2,
            'quality' => 0.9,
        ];
        $options = array_merge($defaultOptions, $options);

        $result = $this->posterServer->generateImage($dataId, $options);
        
        if ($result['code'] === 200) {
            $this->success($result['data'], '图片生成成功');
        } else {
            $this->error($result['message']);
        }
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    private function getUserId()
    {
        // 这里应该从JWT token或session中获取用户ID
        // 暂时返回0表示匿名用户
        return 0;
    }
}
