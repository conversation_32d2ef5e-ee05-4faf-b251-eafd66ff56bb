<?php

namespace app\api\controller;

use think\Controller;
use think\facade\Request;
use think\facade\Validate;

/**
 * API基础控制器
 * Class BaseApi
 * @package app\api\controller
 */
class BaseApi extends Controller
{
    /**
     * 前置操作
     */
    public function initialize()
    {
        parent::initialize();
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        
        // 处理OPTIONS请求
        if (Request::method() === 'OPTIONS') {
            exit;
        }
    }

    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应码
     */
    protected function success($data = [], $message = 'success', $code = 200)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param mixed $data 错误数据
     * @param int $code 错误码
     */
    protected function error($message = 'error', $data = [], $code = 400)
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 参数验证
     * @param array $data 待验证数据
     * @param array $rules 验证规则
     * @param array $messages 错误消息
     * @return bool|string
     */
    protected function validate($data, $rules, $messages = [])
    {
        $validate = Validate::make($rules, $messages);
        
        if (!$validate->check($data)) {
            return $validate->getError();
        }
        
        return true;
    }

    /**
     * 获取分页参数
     * @param int $defaultLimit 默认每页数量
     * @return array
     */
    protected function getPageParams($defaultLimit = 20)
    {
        $page = (int)$this->request->param('page', 1);
        $limit = (int)$this->request->param('limit', $defaultLimit);
        
        $page = max(1, $page);
        $limit = max(1, min(100, $limit)); // 限制最大100条
        
        return [$page, $limit];
    }

    /**
     * 获取排序参数
     * @param string $defaultField 默认排序字段
     * @param string $defaultOrder 默认排序方向
     * @return array
     */
    protected function getSortParams($defaultField = 'create_time', $defaultOrder = 'desc')
    {
        $field = $this->request->param('sort_field', $defaultField);
        $order = $this->request->param('sort_order', $defaultOrder);
        
        // 安全检查，防止SQL注入
        $allowedFields = ['id', 'create_time', 'update_time', 'name', 'title'];
        $allowedOrders = ['asc', 'desc'];
        
        if (!in_array($field, $allowedFields)) {
            $field = $defaultField;
        }
        
        if (!in_array(strtolower($order), $allowedOrders)) {
            $order = $defaultOrder;
        }
        
        return [$field, $order];
    }

    /**
     * 获取搜索参数
     * @return array
     */
    protected function getSearchParams()
    {
        $keyword = trim($this->request->param('keyword', ''));
        $startTime = $this->request->param('start_time', '');
        $endTime = $this->request->param('end_time', '');
        
        $params = [];
        
        if (!empty($keyword)) {
            $params['keyword'] = $keyword;
        }
        
        if (!empty($startTime)) {
            $params['start_time'] = $startTime;
        }
        
        if (!empty($endTime)) {
            $params['end_time'] = $endTime;
        }
        
        return $params;
    }

    /**
     * 记录操作日志
     * @param string $action 操作动作
     * @param mixed $data 操作数据
     */
    protected function logOperation($action, $data = [])
    {
        $logData = [
            'action' => $action,
            'controller' => $this->request->controller(),
            'method' => $this->request->action(),
            'url' => $this->request->url(true),
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent'),
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s'),
        ];

        \think\facade\Log::info('API操作日志', $logData);
    }

    /**
     * 检查必需参数
     * @param array $params 参数数组
     * @param array $required 必需参数列表
     * @return bool|string
     */
    protected function checkRequired($params, $required)
    {
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                return "参数 {$field} 不能为空";
            }
        }
        
        return true;
    }

    /**
     * 过滤参数
     * @param array $params 原始参数
     * @param array $allowed 允许的参数
     * @return array
     */
    protected function filterParams($params, $allowed)
    {
        $filtered = [];
        
        foreach ($allowed as $field) {
            if (isset($params[$field])) {
                $filtered[$field] = $params[$field];
            }
        }
        
        return $filtered;
    }

    /**
     * 格式化响应数据
     * @param mixed $data 原始数据
     * @param array $fields 需要的字段
     * @return array
     */
    protected function formatResponseData($data, $fields = [])
    {
        if (empty($fields)) {
            return $data;
        }
        
        if (is_array($data) && isset($data[0])) {
            // 数组数据
            $formatted = [];
            foreach ($data as $item) {
                $formatted[] = $this->pickFields($item, $fields);
            }
            return $formatted;
        } else {
            // 单个数据
            return $this->pickFields($data, $fields);
        }
    }

    /**
     * 提取指定字段
     * @param array $data 数据
     * @param array $fields 字段列表
     * @return array
     */
    private function pickFields($data, $fields)
    {
        $result = [];
        
        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $result[$field] = $data[$field];
            }
        }
        
        return $result;
    }
}
