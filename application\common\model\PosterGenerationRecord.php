<?php

namespace app\common\model;

use think\Model;

/**
 * 海报图片生成记录模型
 * Class PosterGenerationRecord
 * @package app\common\model
 */
class PosterGenerationRecord extends Model
{
    protected $name = 'poster_generation_records';
    protected $pk = 'id';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false;

    // JSON字段
    protected $json = ['generation_options'];
    protected $jsonAssoc = true;

    /**
     * 生成选项获取器
     * @param $value
     * @return array
     */
    public function getGenerationOptionsAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 生成选项设置器
     * @param $value
     * @return string
     */
    public function setGenerationOptionsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * 文件大小格式化获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getFileSizeFormatAttr($value, $data)
    {
        $size = $data['file_size'] ?? 0;
        if ($size < 1024) {
            return $size . ' B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . ' KB';
        } else {
            return round($size / (1024 * 1024), 2) . ' MB';
        }
    }

    /**
     * 关联用户数据
     * @return \think\model\relation\BelongsTo
     */
    public function userData()
    {
        return $this->belongsTo(PosterUserData::class, 'data_id', 'id');
    }

    /**
     * 创建生成记录
     * @param array $data
     * @return PosterGenerationRecord|false
     */
    public static function createRecord($data)
    {
        $record = new self();
        $record->id = $data['id'] ?? self::generateId();
        $record->data_id = $data['data_id'];
        $record->image_url = $data['image_url'];
        $record->generation_options = $data['generation_options'] ?? [];
        $record->generation_time = $data['generation_time'] ?? null;
        $record->file_size = $data['file_size'] ?? null;
        
        return $record->save() ? $record : false;
    }

    /**
     * 获取记录列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getList($where = [], $page = 1, $limit = 20)
    {
        $query = self::with(['userData']);
        
        if (!empty($where['data_id'])) {
            $query->where('data_id', $where['data_id']);
        }
        
        if (!empty($where['start_time'])) {
            $query->where('create_time', '>=', strtotime($where['start_time']));
        }
        
        if (!empty($where['end_time'])) {
            $query->where('create_time', '<=', strtotime($where['end_time']));
        }

        $total = $query->count();
        $list = $query->order('create_time desc')
                     ->page($page, $limit)
                     ->select()
                     ->toArray();

        return [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
        ];
    }

    /**
     * 生成唯一ID
     * @return string
     */
    public static function generateId()
    {
        return 'record_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 获取统计数据
     * @param array $where
     * @return array
     */
    public static function getStatistics($where = [])
    {
        $query = self::query();
        
        if (!empty($where['start_time'])) {
            $query->where('create_time', '>=', strtotime($where['start_time']));
        }
        
        if (!empty($where['end_time'])) {
            $query->where('create_time', '<=', strtotime($where['end_time']));
        }

        $total = $query->count();
        $avgTime = $query->avg('generation_time');
        $totalSize = $query->sum('file_size');

        return [
            'total_count' => $total,
            'avg_generation_time' => round($avgTime, 3),
            'total_file_size' => $totalSize,
            'avg_file_size' => $total > 0 ? round($totalSize / $total, 0) : 0,
        ];
    }
}
