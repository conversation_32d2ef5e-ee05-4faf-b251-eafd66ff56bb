# 动态参数模板功能使用指南

## 📋 功能概述

动态参数模板系统是一个强大的海报生成工具，允许用户创建可配置的模板，并通过参数化的方式生成个性化海报。

## 🎯 主要功能

### 1. 模板配置管理
- 创建和管理海报模板配置
- 定义可变参数和验证规则
- 设置参数标签和显示顺序

### 2. 用户数据管理
- 用户提交参数数据
- 草稿和正式数据管理
- 参数值验证和存储

### 3. API接口服务
- 外部系统集成接口
- 内部管理接口
- 安全认证机制

## 🚀 快速开始

### 第一步：创建模板配置

```php
// 使用模型创建配置
$configData = [
    'template_id' => '2',                    // 模板ID
    'template_title' => '个人名片模板',        // 模板标题
    'config_name' => '标准名片配置',          // 配置名称
    'config_description' => '适用于个人名片的标准配置', // 配置描述
    'parameters' => [                        // 参数定义
        [
            'id' => 'name-param',
            'elementUuid' => 'name-element-uuid',
            'parameterName' => 'user_name',
            'parameterLabel' => '姓名',
            'parameterType' => 'text',
            'isRequired' => true,
            'isEnabled' => true,
            'displayOrder' => 1,
        ],
        [
            'id' => 'title-param',
            'elementUuid' => 'title-element-uuid',
            'parameterName' => 'job_title',
            'parameterLabel' => '职位',
            'parameterType' => 'text',
            'isRequired' => false,
            'isEnabled' => true,
            'displayOrder' => 2,
        ]
    ],
    'created_by' => 1,                       // 创建者ID
];

$config = \app\common\model\PosterTemplateConfig::createConfig($configData);
```

### 第二步：用户提交数据

```php
// 用户提交参数数据
$userData = [
    'config_id' => $config->id,             // 配置ID
    'template_id' => '2',                   // 模板ID
    'user_id' => 123,                       // 用户ID
    'parameter_values' => [                 // 参数值
        'user_name' => '张三',
        'job_title' => '产品经理'
    ],
    'is_draft' => 0,                        // 0=正式，1=草稿
];

$userDataModel = \app\common\model\PosterUserData::createData($userData);
```

## 🔌 API接口使用

### 外部API接口（供第三方系统调用）

#### 1. 健康检查
```bash
GET /api/external/health
Headers: Authorization: Bearer your_api_key
```

#### 2. 获取参数配置
```bash
GET /api/external/parameter-config/{config_id}
Headers: Authorization: Bearer your_api_key
```

#### 3. 获取参数数据
```bash
GET /api/external/parameter-data/{data_id}
Headers: Authorization: Bearer your_api_key
```

### 内部API接口（供主项目使用）

#### 1. 模板解析
```bash
POST /api/poster/parse-template
Content-Type: application/json

{
    "template_id": "2"
}
```

#### 2. 创建配置
```bash
POST /api/poster/create-config
Content-Type: application/json

{
    "template_id": "2",
    "config_name": "新配置",
    "config_description": "配置描述",
    "selected_parameters": ["param-1", "param-2"],
    "parameter_labels": {
        "param-1": "参数1标签",
        "param-2": "参数2标签"
    },
    "parameter_required": {
        "param-1": true,
        "param-2": false
    }
}
```

#### 3. 获取配置列表
```bash
GET /api/poster/config-list?page=1&limit=10
```

#### 4. 获取配置详情
```bash
GET /api/poster/config-detail/{config_id}
```

#### 5. 提交用户数据
```bash
POST /api/poster/submit-data
Content-Type: application/json

{
    "config_id": "config_id_here",
    "parameter_values": {
        "user_name": "张三",
        "job_title": "产品经理"
    },
    "is_draft": 0
}
```

#### 6. 获取用户数据详情
```bash
GET /api/poster/user-data-detail/{data_id}
```

## 🎨 前端页面集成示例

### 1. 配置管理页面

```html
<!-- 配置列表页面 -->
<div class="config-list">
    <h2>模板配置管理</h2>
    <button onclick="createConfig()">创建新配置</button>
    <table id="configTable">
        <thead>
            <tr>
                <th>配置名称</th>
                <th>模板标题</th>
                <th>参数数量</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 动态加载配置列表 -->
        </tbody>
    </table>
</div>

<script>
// 加载配置列表
function loadConfigs() {
    fetch('/api/poster/config-list?page=1&limit=10')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderConfigTable(data.data.list);
            }
        });
}

// 创建新配置
function createConfig() {
    const configData = {
        template_id: '2',
        config_name: '新配置名称',
        config_description: '配置描述',
        selected_parameters: ['param-1'],
        parameter_labels: {'param-1': '参数标签'},
        parameter_required: {'param-1': true}
    };
    
    fetch('/api/poster/create-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert('配置创建成功');
            loadConfigs(); // 重新加载列表
        }
    });
}
</script>
```

### 2. 用户数据提交页面

```html
<!-- 用户数据提交页面 -->
<div class="data-submit">
    <h2>填写海报参数</h2>
    <form id="parameterForm">
        <div class="form-group">
            <label for="user_name">姓名 *</label>
            <input type="text" id="user_name" name="user_name" required>
        </div>
        <div class="form-group">
            <label for="job_title">职位</label>
            <input type="text" id="job_title" name="job_title">
        </div>
        <div class="form-actions">
            <button type="button" onclick="saveDraft()">保存草稿</button>
            <button type="button" onclick="submitData()">提交生成</button>
        </div>
    </form>
</div>

<script>
// 保存草稿
function saveDraft() {
    submitUserData(1); // is_draft = 1
}

// 提交正式数据
function submitData() {
    submitUserData(0); // is_draft = 0
}

// 提交用户数据
function submitUserData(isDraft) {
    const formData = new FormData(document.getElementById('parameterForm'));
    const parameterValues = {};
    
    for (let [key, value] of formData.entries()) {
        parameterValues[key] = value;
    }
    
    const submitData = {
        config_id: 'your_config_id_here',
        parameter_values: parameterValues,
        is_draft: isDraft
    };
    
    fetch('/api/poster/submit-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert(isDraft ? '草稿保存成功' : '数据提交成功');
            if (!isDraft) {
                // 跳转到预览页面或生成页面
                window.location.href = '/poster/preview/' + data.data.data_id;
            }
        }
    });
}
</script>
```

### 3. 数据管理页面

```html
<!-- 用户数据管理页面 -->
<div class="data-management">
    <h2>用户数据管理</h2>
    <div class="filters">
        <select id="configFilter">
            <option value="">选择配置</option>
            <!-- 动态加载配置选项 -->
        </select>
        <select id="statusFilter">
            <option value="">全部状态</option>
            <option value="1">草稿</option>
            <option value="0">已提交</option>
        </select>
        <button onclick="loadUserData()">查询</button>
    </div>
    <table id="userDataTable">
        <thead>
            <tr>
                <th>数据ID</th>
                <th>配置名称</th>
                <th>用户ID</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 动态加载用户数据 -->
        </tbody>
    </table>
</div>

<script>
// 查看数据详情
function viewDataDetail(dataId) {
    fetch(`/api/poster/user-data-detail/${dataId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showDataDetailModal(data.data);
            }
        });
}

// 显示数据详情模态框
function showDataDetailModal(data) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h3>数据详情</h3>
            <p><strong>数据ID:</strong> ${data.id}</p>
            <p><strong>配置ID:</strong> ${data.config_id}</p>
            <p><strong>用户ID:</strong> ${data.user_id}</p>
            <p><strong>状态:</strong> ${data.is_draft ? '草稿' : '已提交'}</p>
            <h4>参数值:</h4>
            <pre>${JSON.stringify(data.parameter_values, null, 2)}</pre>
            <button onclick="closeModal()">关闭</button>
        </div>
    `;
    document.body.appendChild(modal);
}
</script>
```

## 🔐 安全注意事项

### 1. API密钥管理
- 外部API调用必须使用有效的API密钥
- API密钥应该定期更换
- 不要在前端代码中暴露API密钥

### 2. 参数验证
- 所有用户输入都会进行验证
- 必需参数不能为空
- 参数类型必须匹配定义

### 3. 权限控制
- 用户只能访问自己的数据
- 管理员可以访问所有配置
- API接口有访问频率限制

## 🛠️ 故障排查

### 常见问题

1. **配置创建失败**
   - 检查参数格式是否正确
   - 确认模板ID是否存在
   - 验证用户权限

2. **数据提交失败**
   - 检查配置ID是否有效
   - 验证必需参数是否填写
   - 确认参数值格式正确

3. **API调用失败**
   - 检查API密钥是否正确
   - 确认请求头格式
   - 验证请求参数

## 📞 技术支持

如有问题，请查看：
- 测试报告：`docs/API功能测试报告.md`
- 技术文档：`docs/动态参数模板系统技术实现文档.md`
- 数据库文档：`docs/数据库设计文档.md`

---

**版本**: v1.0  
**更新时间**: 2025年8月17日
