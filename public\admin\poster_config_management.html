<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报模板配置管理</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .container { padding: 20px; }
        .header { margin-bottom: 20px; }
        .config-form { background: #fff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .parameter-item { border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 10px; border-radius: 5px; }
        .parameter-item:last-child { margin-bottom: 0; }
        .btn-group { text-align: right; margin-top: 20px; }
        .layui-table-cell { height: auto !important; }
        .status-active { color: #5FB878; }
        .status-inactive { color: #FF5722; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>海报模板配置管理</h2>
            <button class="layui-btn" onclick="showCreateForm()">
                <i class="layui-icon layui-icon-add-1"></i> 创建新配置
            </button>
        </div>

        <!-- 配置列表 -->
        <div class="layui-card">
            <div class="layui-card-header">配置列表</div>
            <div class="layui-card-body">
                <table class="layui-table" lay-filter="configTable" id="configTable"></table>
            </div>
        </div>

        <!-- 创建/编辑配置表单 -->
        <div class="layui-layer" id="configFormLayer" style="display: none;">
            <div class="config-form">
                <h3 id="formTitle">创建新配置</h3>
                <form class="layui-form" id="configForm">
                    <input type="hidden" id="configId" name="id">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">模板ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="template_id" required lay-verify="required" 
                                   placeholder="请输入模板ID" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">模板标题</label>
                        <div class="layui-input-block">
                            <input type="text" name="template_title" placeholder="请输入模板标题" 
                                   autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">配置名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="config_name" required lay-verify="required" 
                                   placeholder="请输入配置名称" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">配置描述</label>
                        <div class="layui-input-block">
                            <textarea name="config_description" placeholder="请输入配置描述" 
                                      class="layui-textarea"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">参数配置</label>
                        <div class="layui-input-block">
                            <div id="parametersContainer">
                                <!-- 参数配置项将在这里动态生成 -->
                            </div>
                            <button type="button" class="layui-btn layui-btn-normal" onclick="addParameter()">
                                <i class="layui-icon layui-icon-add-1"></i> 添加参数
                            </button>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="layui-btn layui-btn-primary" onclick="closeForm()">取消</button>
                        <button type="submit" class="layui-btn" lay-submit lay-filter="configSubmit">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'element'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var element = layui.element;

            // 配置列表表格
            table.render({
                elem: '#configTable',
                url: '/api/poster/config-list',
                page: true,
                cols: [[
                    {field: 'id', title: 'ID', width: 200, sort: true},
                    {field: 'config_name', title: '配置名称', width: 200},
                    {field: 'template_title', title: '模板标题', width: 150},
                    {field: 'template_id', title: '模板ID', width: 100},
                    {field: 'parameter_count', title: '参数数量', width: 100, templet: function(d){
                        try {
                            var params = JSON.parse(d.parameters || '[]');
                            return params.length;
                        } catch(e) {
                            return 0;
                        }
                    }},
                    {field: 'status', title: '状态', width: 80, templet: function(d){
                        return d.status == 1 ? '<span class="status-active">启用</span>' : '<span class="status-inactive">禁用</span>';
                    }},
                    {field: 'create_time', title: '创建时间', width: 160, templet: function(d){
                        return new Date(d.create_time * 1000).toLocaleString();
                    }},
                    {title: '操作', width: 200, toolbar: '#operationBar'}
                ]],
                parseData: function(res){
                    return {
                        "code": res.code === 200 ? 0 : res.code,
                        "msg": res.msg || res.message,
                        "count": res.data ? res.data.total : 0,
                        "data": res.data ? res.data.list : []
                    };
                }
            });

            // 操作栏模板
            var operationBarTpl = `
                <script type="text/html" id="operationBar">
                    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
                </script>
            `;
            $('body').append(operationBarTpl);

            // 监听工具条
            table.on('tool(configTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'edit'){
                    editConfig(data);
                } else if(obj.event === 'view'){
                    viewConfig(data);
                } else if(obj.event === 'delete'){
                    deleteConfig(data);
                }
            });

            // 表单提交
            form.on('submit(configSubmit)', function(data){
                saveConfig(data.field);
                return false;
            });

            // 全局变量
            window.parameterIndex = 0;

            // 显示创建表单
            window.showCreateForm = function() {
                document.getElementById('formTitle').textContent = '创建新配置';
                document.getElementById('configForm').reset();
                document.getElementById('configId').value = '';
                document.getElementById('parametersContainer').innerHTML = '';
                addParameter(); // 默认添加一个参数
                
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    area: ['800px', '600px'],
                    content: $('#configFormLayer'),
                    success: function(){
                        form.render();
                    }
                });
            };

            // 编辑配置
            window.editConfig = function(data) {
                document.getElementById('formTitle').textContent = '编辑配置';
                document.getElementById('configId').value = data.id;
                
                // 填充表单数据
                $('input[name="template_id"]').val(data.template_id);
                $('input[name="template_title"]').val(data.template_title);
                $('input[name="config_name"]').val(data.config_name);
                $('textarea[name="config_description"]').val(data.config_description);
                
                // 填充参数数据
                var parametersContainer = document.getElementById('parametersContainer');
                parametersContainer.innerHTML = '';
                
                try {
                    var parameters = JSON.parse(data.parameters || '[]');
                    parameters.forEach(function(param) {
                        addParameter(param);
                    });
                } catch(e) {
                    console.error('解析参数数据失败:', e);
                    addParameter();
                }
                
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    area: ['800px', '600px'],
                    content: $('#configFormLayer'),
                    success: function(){
                        form.render();
                    }
                });
            };

            // 查看配置详情
            window.viewConfig = function(data) {
                var content = `
                    <div style="padding: 20px;">
                        <h3>配置详情</h3>
                        <p><strong>配置ID:</strong> ${data.id}</p>
                        <p><strong>配置名称:</strong> ${data.config_name}</p>
                        <p><strong>模板ID:</strong> ${data.template_id}</p>
                        <p><strong>模板标题:</strong> ${data.template_title || '未设置'}</p>
                        <p><strong>配置描述:</strong> ${data.config_description || '无'}</p>
                        <p><strong>状态:</strong> ${data.status == 1 ? '启用' : '禁用'}</p>
                        <p><strong>创建时间:</strong> ${new Date(data.create_time * 1000).toLocaleString()}</p>
                        <h4>参数配置:</h4>
                        <pre style="background: #f5f5f5; padding: 10px; border-radius: 3px;">${JSON.stringify(JSON.parse(data.parameters || '[]'), null, 2)}</pre>
                    </div>
                `;
                
                layer.open({
                    type: 1,
                    title: '配置详情',
                    area: ['600px', '500px'],
                    content: content
                });
            };

            // 删除配置
            window.deleteConfig = function(data) {
                layer.confirm('确定要删除这个配置吗？', {icon: 3, title:'提示'}, function(index){
                    // 这里应该调用删除API
                    layer.msg('删除功能待实现', {icon: 1});
                    layer.close(index);
                });
            };

            // 添加参数
            window.addParameter = function(paramData) {
                var index = window.parameterIndex++;
                var param = paramData || {
                    id: '',
                    elementUuid: '',
                    parameterName: '',
                    parameterLabel: '',
                    parameterType: 'text',
                    isRequired: false,
                    isEnabled: true,
                    displayOrder: index + 1
                };

                var parameterHtml = `
                    <div class="parameter-item" data-index="${index}">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <label class="layui-form-label">参数名称</label>
                                <div class="layui-input-block">
                                    <input type="text" name="parameters[${index}][parameterName]" 
                                           value="${param.parameterName}" placeholder="参数名称" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <label class="layui-form-label">参数标签</label>
                                <div class="layui-input-block">
                                    <input type="text" name="parameters[${index}][parameterLabel]" 
                                           value="${param.parameterLabel}" placeholder="显示标签" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md4">
                                <label class="layui-form-label">参数类型</label>
                                <div class="layui-input-block">
                                    <select name="parameters[${index}][parameterType]">
                                        <option value="text" ${param.parameterType === 'text' ? 'selected' : ''}>文本</option>
                                        <option value="number" ${param.parameterType === 'number' ? 'selected' : ''}>数字</option>
                                        <option value="email" ${param.parameterType === 'email' ? 'selected' : ''}>邮箱</option>
                                        <option value="url" ${param.parameterType === 'url' ? 'selected' : ''}>网址</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <label class="layui-form-label">是否必填</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="parameters[${index}][isRequired]" 
                                           ${param.isRequired ? 'checked' : ''} lay-skin="switch" lay-text="是|否">
                                </div>
                            </div>
                            <div class="layui-col-md4">
                                <label class="layui-form-label">是否启用</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" name="parameters[${index}][isEnabled]" 
                                           ${param.isEnabled ? 'checked' : ''} lay-skin="switch" lay-text="是|否">
                                </div>
                            </div>
                        </div>
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <label class="layui-form-label">元素UUID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="parameters[${index}][elementUuid]" 
                                           value="${param.elementUuid}" placeholder="元素UUID" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <label class="layui-form-label">显示顺序</label>
                                <div class="layui-input-block">
                                    <input type="number" name="parameters[${index}][displayOrder]" 
                                           value="${param.displayOrder}" placeholder="显示顺序" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="parameters[${index}][id]" value="${param.id}">
                        <div style="text-align: right; margin-top: 10px;">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" 
                                    onclick="removeParameter(${index})">删除参数</button>
                        </div>
                    </div>
                `;

                document.getElementById('parametersContainer').insertAdjacentHTML('beforeend', parameterHtml);
                form.render();
            };

            // 删除参数
            window.removeParameter = function(index) {
                var parameterItem = document.querySelector(`.parameter-item[data-index="${index}"]`);
                if (parameterItem) {
                    parameterItem.remove();
                }
            };

            // 关闭表单
            window.closeForm = function() {
                layer.closeAll();
            };

            // 保存配置
            window.saveConfig = function(formData) {
                // 处理参数数据
                var parameters = [];
                var parameterInputs = document.querySelectorAll('input[name^="parameters["]');
                var parameterSelects = document.querySelectorAll('select[name^="parameters["]');
                var parameterCheckboxes = document.querySelectorAll('input[type="checkbox"][name^="parameters["]');
                
                // 收集参数数据
                var paramMap = {};
                
                // 处理文本输入
                parameterInputs.forEach(function(input) {
                    var match = input.name.match(/parameters\[(\d+)\]\[(\w+)\]/);
                    if (match) {
                        var index = match[1];
                        var field = match[2];
                        if (!paramMap[index]) paramMap[index] = {};
                        paramMap[index][field] = input.value;
                    }
                });
                
                // 处理选择框
                parameterSelects.forEach(function(select) {
                    var match = select.name.match(/parameters\[(\d+)\]\[(\w+)\]/);
                    if (match) {
                        var index = match[1];
                        var field = match[2];
                        if (!paramMap[index]) paramMap[index] = {};
                        paramMap[index][field] = select.value;
                    }
                });
                
                // 处理复选框
                parameterCheckboxes.forEach(function(checkbox) {
                    var match = checkbox.name.match(/parameters\[(\d+)\]\[(\w+)\]/);
                    if (match) {
                        var index = match[1];
                        var field = match[2];
                        if (!paramMap[index]) paramMap[index] = {};
                        paramMap[index][field] = checkbox.checked;
                    }
                });
                
                // 转换为数组
                Object.keys(paramMap).forEach(function(index) {
                    parameters.push(paramMap[index]);
                });

                var submitData = {
                    template_id: formData.template_id,
                    template_title: formData.template_title,
                    config_name: formData.config_name,
                    config_description: formData.config_description,
                    parameters: parameters
                };

                var url = formData.id ? '/api/poster/update-config' : '/api/poster/create-config';
                if (formData.id) {
                    submitData.id = formData.id;
                }

                // 发送请求
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(submitData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        layer.msg('保存成功', {icon: 1});
                        layer.closeAll();
                        table.reload('configTable'); // 重新加载表格
                    } else {
                        layer.msg(data.msg || '保存失败', {icon: 2});
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    layer.msg('网络错误', {icon: 2});
                });
            };
        });
    </script>
</body>
</html>
