# 动态参数模板系统 API 接口详细文档

## 📋 文档概述

本文档详细描述了动态参数模板系统的所有API接口，包括请求参数、响应格式、错误码说明和调用示例。

**API版本**: v1.0  
**基础URL**: `http://your-domain.com`  
**文档更新时间**: 2025年8月17日

## 🔐 认证方式

### 外部API认证
外部API接口需要在请求头中包含API密钥：

```http
Authorization: Bearer your_api_key_here
```

### 内部API认证
内部API接口通常不需要特殊认证，但建议在生产环境中添加适当的认证机制。

## 📊 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
    "code": 200,
    "msg": "success",
    "data": {
        // 具体的响应数据
    },
    "timestamp": **********
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | int | 响应状态码，200表示成功 |
| msg | string | 响应消息 |
| data | object/array | 响应数据，具体结构因接口而异 |
| timestamp | int | 响应时间戳 |

## 🔢 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 认证失败 | 检查API密钥是否正确 |
| 403 | 权限不足 | 检查API密钥权限 |
| 404 | 资源不存在 | 检查请求的资源ID是否正确 |
| 422 | 数据验证失败 | 检查提交的数据是否符合验证规则 |
| 500 | 服务器内部错误 | 联系技术支持 |
| 503 | 服务不可用 | 稍后重试或联系技术支持 |

## 🌐 外部API接口

### 1. 健康检查

**接口描述**: 检查API服务状态

**请求信息**:
- **URL**: `/api/external/health`
- **方法**: `GET`
- **认证**: 需要API密钥

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/external/health" \
  -H "Authorization: Bearer your_api_key_here"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "status": "ok",
        "version": "1.0.0",
        "timestamp": **********,
        "server_time": "2025-08-17 12:00:00"
    }
}
```

### 2. 获取参数配置

**接口描述**: 根据配置ID获取模板参数配置信息

**请求信息**:
- **URL**: `/api/external/parameter-config/{config_id}`
- **方法**: `GET`
- **认证**: 需要API密钥

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| config_id | string | 是 | 配置ID |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/external/parameter-config/CONFIG_123" \
  -H "Authorization: Bearer your_api_key_here"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "config_id": "CONFIG_123",
        "config_name": "个人名片配置",
        "template_id": "2",
        "template_title": "个人名片模板",
        "parameters": [
            {
                "id": "name-param",
                "elementUuid": "name-element-uuid",
                "parameterName": "user_name",
                "parameterLabel": "姓名",
                "parameterType": "text",
                "isRequired": true,
                "isEnabled": true,
                "displayOrder": 1
            }
        ],
        "created_time": "2025-08-17 12:00:00"
    }
}
```

### 3. 获取参数数据

**接口描述**: 根据数据ID获取用户提交的参数数据

**请求信息**:
- **URL**: `/api/external/parameter-data/{data_id}`
- **方法**: `GET`
- **认证**: 需要API密钥

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| data_id | string | 是 | 数据ID |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/external/parameter-data/DATA_123" \
  -H "Authorization: Bearer your_api_key_here"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "data_id": "DATA_123",
        "config_id": "CONFIG_123",
        "template_id": "2",
        "user_id": 1001,
        "parameter_values": {
            "user_name": "张三",
            "job_title": "产品经理"
        },
        "is_draft": 0,
        "created_time": "2025-08-17 12:00:00",
        "updated_time": "2025-08-17 12:05:00"
    }
}
```

## 🏠 内部API接口

### 1. 模板解析

**接口描述**: 解析指定模板的参数信息

**请求信息**:
- **URL**: `/api/poster/parse-template`
- **方法**: `POST`
- **认证**: 无需特殊认证

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| template_id | string | 是 | 模板ID |

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/poster/parse-template" \
  -H "Content-Type: application/json" \
  -d '{
    "template_id": "2"
  }'
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "template_id": "2",
        "template_title": "个人名片模板",
        "available_parameters": [
            {
                "id": "param-1",
                "elementUuid": "element-uuid-1",
                "parameterName": "user_name",
                "parameterLabel": "姓名",
                "parameterType": "text",
                "defaultValue": "",
                "description": "用户的真实姓名"
            }
        ]
    }
}
```

### 2. 创建配置

**接口描述**: 创建新的模板配置

**请求信息**:
- **URL**: `/api/poster/create-config`
- **方法**: `POST`
- **认证**: 无需特殊认证

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| template_id | string | 是 | 模板ID |
| template_title | string | 否 | 模板标题 |
| config_name | string | 是 | 配置名称 |
| config_description | string | 否 | 配置描述 |
| parameters | array | 是 | 参数配置数组 |

**参数配置对象结构**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 否 | 参数ID |
| elementUuid | string | 是 | 元素UUID |
| parameterName | string | 是 | 参数名称 |
| parameterLabel | string | 是 | 参数标签 |
| parameterType | string | 是 | 参数类型(text/number/email等) |
| isRequired | boolean | 否 | 是否必填，默认false |
| isEnabled | boolean | 否 | 是否启用，默认true |
| displayOrder | int | 否 | 显示顺序，默认1 |

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/poster/create-config" \
  -H "Content-Type: application/json" \
  -d '{
    "template_id": "2",
    "template_title": "个人名片模板",
    "config_name": "标准名片配置",
    "config_description": "适用于个人名片的标准配置",
    "parameters": [
        {
            "elementUuid": "name-element-uuid",
            "parameterName": "user_name",
            "parameterLabel": "姓名",
            "parameterType": "text",
            "isRequired": true,
            "isEnabled": true,
            "displayOrder": 1
        }
    ]
  }'
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "配置创建成功",
    "data": {
        "config_id": "CONFIG_**********_ABC123",
        "template_id": "2",
        "config_name": "标准名片配置",
        "parameter_count": 1,
        "created_time": "2025-08-17 12:00:00"
    }
}
```

### 3. 获取配置列表

**接口描述**: 获取模板配置列表

**请求信息**:
- **URL**: `/api/poster/config-list`
- **方法**: `GET`
- **认证**: 无需特殊认证

**查询参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认20 |
| template_id | string | 否 | 模板ID筛选 |
| status | int | 否 | 状态筛选(0/1) |
| keyword | string | 否 | 关键词搜索 |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/poster/config-list?page=1&limit=10&template_id=2"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "list": [
            {
                "id": "CONFIG_123",
                "template_id": "2",
                "template_title": "个人名片模板",
                "config_name": "标准名片配置",
                "config_description": "适用于个人名片的标准配置",
                "parameter_count": 2,
                "status": 1,
                "created_by": 1,
                "create_time": **********,
                "update_time": **********
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 10,
        "pages": 1
    }
}
```

### 4. 获取配置详情

**接口描述**: 根据配置ID获取详细信息

**请求信息**:
- **URL**: `/api/poster/config-detail/{config_id}`
- **方法**: `GET`
- **认证**: 无需特殊认证

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| config_id | string | 是 | 配置ID |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/poster/config-detail/CONFIG_123"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "id": "CONFIG_123",
        "template_id": "2",
        "template_title": "个人名片模板",
        "config_name": "标准名片配置",
        "config_description": "适用于个人名片的标准配置",
        "parameters": [
            {
                "id": "name-param",
                "elementUuid": "name-element-uuid",
                "parameterName": "user_name",
                "parameterLabel": "姓名",
                "parameterType": "text",
                "isRequired": true,
                "isEnabled": true,
                "displayOrder": 1
            }
        ],
        "status": 1,
        "created_by": 1,
        "create_time": **********,
        "update_time": **********
    }
}
```

### 5. 提交用户数据

**接口描述**: 用户提交参数数据

**请求信息**:
- **URL**: `/api/poster/submit-data`
- **方法**: `POST`
- **认证**: 无需特殊认证

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| config_id | string | 是 | 配置ID |
| template_id | string | 否 | 模板ID(可从配置中获取) |
| user_id | int | 否 | 用户ID |
| parameter_values | object | 是 | 参数值对象 |
| is_draft | int | 否 | 是否草稿(0/1)，默认0 |

**请求示例**:
```bash
curl -X POST "http://your-domain.com/api/poster/submit-data" \
  -H "Content-Type: application/json" \
  -d '{
    "config_id": "CONFIG_123",
    "user_id": 1001,
    "parameter_values": {
        "user_name": "张三",
        "job_title": "产品经理"
    },
    "is_draft": 0
  }'
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "数据提交成功",
    "data": {
        "data_id": "DATA_**********_XYZ789",
        "config_id": "CONFIG_123",
        "template_id": "2",
        "user_id": 1001,
        "is_draft": 0,
        "created_time": "2025-08-17 12:00:00"
    }
}
```

### 6. 获取用户数据详情

**接口描述**: 根据数据ID获取用户数据详情

**请求信息**:
- **URL**: `/api/poster/user-data-detail/{data_id}`
- **方法**: `GET`
- **认证**: 无需特殊认证

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| data_id | string | 是 | 数据ID |

**请求示例**:
```bash
curl -X GET "http://your-domain.com/api/poster/user-data-detail/DATA_123"
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "id": "DATA_123",
        "config_id": "CONFIG_123",
        "config_name": "标准名片配置",
        "template_id": "2",
        "user_id": 1001,
        "parameter_values": {
            "user_name": "张三",
            "job_title": "产品经理"
        },
        "is_draft": 0,
        "create_time": **********,
        "update_time": **********
    }
}
```

## 🔧 错误处理示例

### 参数验证失败
```json
{
    "code": 422,
    "msg": "参数验证失败",
    "data": {
        "errors": {
            "config_name": ["配置名称不能为空"],
            "parameters": ["参数配置不能为空"]
        }
    }
}
```

### 资源不存在
```json
{
    "code": 404,
    "msg": "配置不存在",
    "data": null
}
```

### 认证失败
```json
{
    "code": 401,
    "msg": "API密钥无效",
    "data": null
}
```

## 📝 调用示例

### JavaScript调用示例

```javascript
// 获取配置列表
async function getConfigList() {
    try {
        const response = await fetch('/api/poster/config-list?page=1&limit=10');
        const data = await response.json();
        
        if (data.code === 200) {
            console.log('配置列表:', data.data.list);
        } else {
            console.error('获取失败:', data.msg);
        }
    } catch (error) {
        console.error('网络错误:', error);
    }
}

// 提交用户数据
async function submitUserData(configId, parameterValues) {
    try {
        const response = await fetch('/api/poster/submit-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                config_id: configId,
                parameter_values: parameterValues,
                is_draft: 0
            })
        });
        
        const data = await response.json();
        
        if (data.code === 200) {
            console.log('提交成功:', data.data);
            return data.data.data_id;
        } else {
            throw new Error(data.msg);
        }
    } catch (error) {
        console.error('提交失败:', error);
        throw error;
    }
}
```

### PHP调用示例

```php
// 外部API调用示例
function callExternalApi($endpoint, $apiKey, $data = null) {
    $url = 'http://your-domain.com' . $endpoint;
    
    $headers = [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return json_decode($response, true);
}

// 获取参数配置
$apiKey = 'your_api_key_here';
$configId = 'CONFIG_123';
$result = callExternalApi("/api/external/parameter-config/{$configId}", $apiKey);

if ($result['code'] === 200) {
    echo "配置获取成功\n";
    print_r($result['data']);
} else {
    echo "获取失败: " . $result['msg'] . "\n";
}
```

## 📞 技术支持

如有API使用问题，请参考：
- 功能使用指南：`docs/动态参数模板功能使用指南.md`
- 测试报告：`docs/API功能测试报告.md`
- 项目状态：`docs/项目完成状态总结.md`

---

**文档版本**: v1.0  
**最后更新**: 2025年8月17日
