{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>用户数据管理</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-sm layui-btn-normal" href="javascript:;" id="exportData">
                <i class="layui-icon layui-icon-export"></i> 导出数据
            </a>
            <a class="layui-btn layui-btn-sm layui-btn-danger" href="javascript:;" id="batchDelete">
                <i class="layui-icon layui-icon-delete"></i> 批量删除
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <form class="layui-form" lay-filter="search">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <select name="config_id" lay-search>
                        <option value="">选择配置</option>
                        {volist name="configs" id="config"}
                        <option value="{$config.id}">{$config.config_name} ({$config.template_title})</option>
                        {/volist}
                    </select>
                </div>
                <div class="layui-col-md2">
                    <select name="is_draft">
                        <option value="">全部状态</option>
                        <option value="1">草稿</option>
                        <option value="0">已提交</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="user_id" placeholder="用户ID" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                </div>
                <div class="layui-col-md2">
                    <button type="reset" class="layui-btn layui-btn-primary">
                        <i class="layui-icon layui-icon-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </form>

        <!-- 统计信息 -->
        <div class="layui-row layui-col-space15" style="margin: 20px 0;">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body" style="text-align: center;">
                        <div style="font-size: 24px; color: #1E9FFF;" id="totalCount">0</div>
                        <div>总数据量</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body" style="text-align: center;">
                        <div style="font-size: 24px; color: #FFB800;" id="draftCount">0</div>
                        <div>草稿数量</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body" style="text-align: center;">
                        <div style="font-size: 24px; color: #5FB878;" id="submittedCount">0</div>
                        <div>已提交数量</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body" style="text-align: center;">
                        <div style="font-size: 24px; color: #FF5722;" id="todayCount">0</div>
                        <div>今日新增</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 操作栏模板 -->
<script type="text/html" id="operationBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view">查看</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </div>
</script>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
    {{# if(d.is_draft == 1) { }}
    <span class="layui-badge layui-bg-orange">草稿</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-green">已提交</span>
    {{# } }}
</script>

</div>

<script>
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    table.render({
        elem: '#dataTable',
        url: '{:url("data_list")}',
        page: true,
        limit: 20,
        limits: [10, 20, 50, 100],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: '数据ID', width: 200, sort: true},
            {field: 'config_name', title: '配置名称', width: 150},
            {field: 'template_title', title: '模板标题', width: 120},
            {field: 'user_id', title: '用户ID', width: 100, sort: true},
            {field: 'parameter_count', title: '参数数量', width: 100, align: 'center'},
            {field: 'is_draft', title: '状态', width: 100, align: 'center', templet: '#statusTpl'},
            {field: 'create_time_text', title: '创建时间', width: 160, sort: true},
            {field: 'update_time_text', title: '更新时间', width: 160, sort: true},
            {title: '操作', width: 200, toolbar: '#operationBar', fixed: 'right'}
        ]],
        done: function(res, curr, count) {
            // 更新统计信息
            updateStatistics();
        }
    });

    // 监听搜索
    form.on('submit(search)', function(data){
        table.reload('dataTable', {
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });

    // 监听工具条
    table.on('tool(dataTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;

        if(layEvent === 'detail'){
            location.href = '{:url("data_detail")}?id=' + data.id;
        } else if(layEvent === 'view'){
            viewDataDetail(data);
        } else if(layEvent === 'delete'){
            layer.confirm('确定要删除这条数据吗？删除后无法恢复！', {icon: 3, title:'提示'}, function(index){
                $.post('{:url("del_data")}', {id: data.id}, function(res){
                    if(res.code == 1){
                        layer.msg('删除成功', {icon: 1});
                        table.reload('dataTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
    });

    // 批量删除
    $('#batchDelete').click(function(){
        var checkStatus = table.checkStatus('dataTable');
        var data = checkStatus.data;
        
        if (data.length === 0) {
            layer.msg('请选择要删除的数据', {icon: 2});
            return;
        }

        var ids = data.map(function(item) {
            return item.id;
        });

        layer.confirm('确定要删除选中的 ' + data.length + ' 条数据吗？删除后无法恢复！', {icon: 3, title:'提示'}, function(index){
            $.post('{:url("batch_del_data")}', {ids: ids}, function(res){
                if(res.code == 1){
                    layer.msg('批量删除成功', {icon: 1});
                    table.reload('dataTable');
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    // 导出数据
    $('#exportData').click(function(){
        layer.confirm('确定要导出当前筛选的数据吗？', {icon: 3, title:'确认导出'}, function(index){
            var searchData = {};
            $('.layui-form input, .layui-form select').each(function(){
                var name = $(this).attr('name');
                var value = $(this).val();
                if (name && value) {
                    searchData[name] = value;
                }
            });

            $.post('{:url("export_data")}', searchData, function(res){
                if(res.code == 1){
                    // 创建下载链接
                    var blob = new Blob([JSON.stringify(res.data, null, 2)], {type: 'application/json'});
                    var url = URL.createObjectURL(blob);
                    var a = document.createElement('a');
                    a.href = url;
                    a.download = 'poster_data_' + new Date().getTime() + '.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    layer.msg('导出成功', {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    // 查看数据详情
    function viewDataDetail(data) {
        $.get('{:url("data_detail")}?id=' + data.id, function(res){
            // 这里应该返回详情数据，暂时用简单的弹窗显示
            var content = '<div style="padding: 20px;">';
            content += '<h3>数据详情</h3>';
            content += '<p><strong>数据ID:</strong> ' + data.id + '</p>';
            content += '<p><strong>配置名称:</strong> ' + data.config_name + '</p>';
            content += '<p><strong>用户ID:</strong> ' + (data.user_id || '未知') + '</p>';
            content += '<p><strong>状态:</strong> ' + data.status_text + '</p>';
            content += '<p><strong>创建时间:</strong> ' + data.create_time_text + '</p>';
            content += '</div>';
            
            layer.open({
                type: 1,
                title: '数据详情',
                area: ['600px', '400px'],
                content: content
            });
        });
    }

    // 更新统计信息
    function updateStatistics() {
        $.get('{:url("statistics")}', function(res){
            if(res.code == 1){
                $('#totalCount').text(res.data.total_data || 0);
                $('#draftCount').text(res.data.draft_data || 0);
                $('#submittedCount').text(res.data.submitted_data || 0);
                $('#todayCount').text(res.data.today_data || 0);
            }
        });
    }

    // 页面加载时更新统计信息
    updateStatistics();
});
</script>
</script>
