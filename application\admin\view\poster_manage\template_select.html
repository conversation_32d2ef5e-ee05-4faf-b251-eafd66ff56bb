{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>选择海报模板</span>
        <a class="layui-btn layui-btn-sm layui-btn-primary fr" href="javascript:history.back();">
            <i class="layui-icon layui-icon-return"></i> 返回
        </a>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <form class="layui-form" lay-filter="searchForm">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md4">
                    <input type="text" name="search" placeholder="搜索模板标题..." autocomplete="off" class="layui-input">
                </div>
                <div class="layui-col-md3">
                    <select name="cate">
                        <option value="">全部分类</option>
                        {volist name="categories" id="category"}
                        <option value="{$category.id}">{$category.name}</option>
                        {/volist}
                    </select>
                </div>
                <div class="layui-col-md2">
                    <select name="type">
                        <option value="0">模板</option>
                        <option value="1">组件</option>
                    </select>
                </div>
                <div class="layui-col-md3">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>

        <!-- 模板列表 -->
        <div class="template-container" style="margin-top: 20px;">
            <div id="templateGrid" class="template-grid">
                <!-- 模板项将在这里动态生成 -->
            </div>
            
            <!-- 分页 -->
            <div id="templatePagination"></div>
        </div>
    </div>
</div>
</div>

<!-- 模板项模板 -->
<script type="text/html" id="templateItemTpl">
    <div class="template-item" data-id="{{d.id}}" data-title="{{d.title}}">
        <div class="template-card">
            <div class="template-image">
                <img src="{{d.thumbnail}}" alt="{{d.title}}" onerror="this.src='/static/admin/images/index.png'">
                <div class="template-overlay">
                    <div class="template-actions">
                        <button class="layui-btn layui-btn-sm layui-btn-normal preview-btn" data-id="{{d.id}}">
                            <i class="layui-icon layui-icon-search"></i> 预览
                        </button>
                        <button class="layui-btn layui-btn-sm select-btn" data-id="{{d.id}}" data-title="{{d.title}}">
                            <i class="layui-icon layui-icon-ok"></i> 选择
                        </button>
                    </div>
                </div>
            </div>
            <div class="template-info">
                <h4 class="template-title">{{d.title}}</h4>
                <p class="template-size">{{d.width}} × {{d.height}}</p>
                {{# if(d.state == 1) { }}
                <span class="template-status active">可用</span>
                {{# } else { }}
                <span class="template-status inactive">不可用</span>
                {{# } }}
            </div>
        </div>
    </div>
</script>

<!-- 模板预览弹窗 -->
<div id="templatePreviewModal" style="display: none; padding: 20px;">
    <div class="preview-container">
        <div class="preview-image">
            <img id="previewImage" src="" alt="模板预览" style="max-width: 100%; max-height: 500px;">
        </div>
        <div class="preview-info" style="margin-top: 15px;">
            <h3 id="previewTitle"></h3>
            <p id="previewSize"></p>
            <div class="preview-actions" style="margin-top: 15px;">
                <button class="layui-btn layui-btn-normal" id="confirmSelect">
                    <i class="layui-icon layui-icon-ok"></i> 确认选择
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.template-item {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.template-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.template-card {
    background: white;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;
}

.template-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-item:hover .template-overlay {
    opacity: 1;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.template-info {
    padding: 15px;
}

.template-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.template-size {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
}

.template-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.template-status.active {
    background: #e8f5e8;
    color: #52c41a;
}

.template-status.inactive {
    background: #fff2f0;
    color: #ff4d4f;
}

.loading-container {
    text-align: center;
    padding: 50px;
    color: #999;
}

.empty-container {
    text-align: center;
    padding: 50px;
    color: #999;
}

.preview-container {
    text-align: center;
}

#previewImage {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
}
</style>

<script>
layui.use(['form', 'laypage', 'layer', 'laytpl', 'jquery'], function(){
    var form = layui.form;
    var laypage = layui.laypage;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;
    
    var currentPage = 1;
    var pageSize = 20;
    var currentParams = {};
    var selectedTemplate = null;
    
    // 初始化
    loadTemplates();
    
    // 搜索
    form.on('submit(search)', function(data){
        currentParams = data.field;
        currentPage = 1;
        loadTemplates();
        return false;
    });
    
    // 加载模板列表
    function loadTemplates() {
        var params = Object.assign({}, currentParams, {
            page: currentPage,
            limit: pageSize
        });
        
        // 显示加载状态
        $('#templateGrid').html('<div class="loading-container"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载中...</div>');
        
        $.get('{:url("template_select")}', params, function(res){
            if(res.code == 1){
                renderTemplates(res.data.lists);
                renderPagination(res.data.count);
            } else {
                $('#templateGrid').html('<div class="empty-container">加载失败: ' + res.msg + '</div>');
            }
        });
    }
    
    // 渲染模板列表
    function renderTemplates(templates) {
        if(!templates || templates.length === 0) {
            $('#templateGrid').html('<div class="empty-container">暂无模板数据</div>');
            return;
        }
        
        var getTpl = document.getElementById('templateItemTpl').innerHTML;
        var html = '';
        
        templates.forEach(function(template) {
            laytpl(getTpl).render(template, function(itemHtml){
                html += itemHtml;
            });
        });
        
        $('#templateGrid').html(html);
    }
    
    // 渲染分页
    function renderPagination(total) {
        if(total <= pageSize) {
            $('#templatePagination').empty();
            return;
        }
        
        laypage.render({
            elem: 'templatePagination',
            count: total,
            limit: pageSize,
            curr: currentPage,
            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
            jump: function(obj, first){
                if(!first){
                    currentPage = obj.curr;
                    pageSize = obj.limit;
                    loadTemplates();
                }
            }
        });
    }
    
    // 预览模板
    $(document).on('click', '.preview-btn', function(){
        var templateId = $(this).data('id');
        var templateItem = $(this).closest('.template-item');
        var title = templateItem.data('title');
        var imgSrc = templateItem.find('img').attr('src');
        
        $('#previewImage').attr('src', imgSrc);
        $('#previewTitle').text(title);
        $('#confirmSelect').data('id', templateId).data('title', title);
        
        layer.open({
            type: 1,
            title: '模板预览',
            content: $('#templatePreviewModal'),
            area: ['600px', '700px'],
            btn: false
        });
    });
    
    // 直接选择模板
    $(document).on('click', '.select-btn', function(){
        var templateId = $(this).data('id');
        var title = $(this).data('title');
        selectTemplate(templateId, title);
    });
    
    // 预览弹窗中确认选择
    $(document).on('click', '#confirmSelect', function(){
        var templateId = $(this).data('id');
        var title = $(this).data('title');
        layer.closeAll();
        selectTemplate(templateId, title);
    });
    
    // 选择模板
    function selectTemplate(templateId, title) {
        // 如果是在弹窗中打开的，向父窗口传递数据
        if(window.parent && window.parent !== window) {
            window.parent.postMessage({
                type: 'templateSelected',
                data: {
                    template_id: templateId,
                    template_title: title
                }
            }, '*');
            
            // 关闭当前弹窗
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        } else {
            // 直接页面跳转的情况，可以通过URL参数传递
            var returnUrl = getUrlParam('return_url') || '{:url("add_config")}';
            location.href = returnUrl + '?template_id=' + templateId + '&template_title=' + encodeURIComponent(title);
        }
    }
    
    // 获取URL参数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
});
</script>
