<?php

namespace app\admin\controller\poster;

use app\admin\controller\AdminBase;
use app\common\model\PosterTemplateConfig;
use app\common\server\PosterServer;

/**
 * 海报模板配置管理
 * Class Config
 * @package app\admin\controller\poster
 */
class Config extends AdminBase
{
    private $posterServer;

    public function initialize()
    {
        parent::initialize();
        $this->posterServer = new PosterServer();
    }

    /**
     * 配置列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $page = $this->request->get('page', 1);
            $limit = $this->request->get('limit', 20);
            $templateId = $this->request->get('template_id', '');
            $keyword = $this->request->get('keyword', '');

            $where = [];
            if (!empty($templateId)) {
                $where['template_id'] = $templateId;
            }
            if (!empty($keyword)) {
                $where['keyword'] = $keyword;
            }

            $result = PosterTemplateConfig::getEnabledList($where, $page, $limit);
            
            $this->_success('获取成功', $result['list'], $result['total']);
        }

        return $this->fetch();
    }

    /**
     * 添加配置
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 参数验证
            if (empty($params['template_id'])) {
                $this->_error('请选择模板');
            }
            if (empty($params['config_name'])) {
                $this->_error('请输入配置名称');
            }

            // 解析模板
            $parseResult = $this->posterServer->parseTemplateAndCreateConfig($params['template_id'], []);
            if ($parseResult['code'] !== 200) {
                $this->_error('模板解析失败：' . $parseResult['message']);
            }

            $this->assign('template_data', $parseResult['data']);
            $this->assign('form_data', $params);
            
            return $this->fetch('config_form');
        }

        return $this->fetch();
    }

    /**
     * 保存配置
     */
    public function save()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 参数验证
            if (empty($params['template_id']) || empty($params['config_name'])) {
                $this->_error('参数不完整');
            }

            // 设置创建者
            $params['created_by'] = $this->admin_id;

            // 创建配置
            $result = $this->posterServer->parseTemplateAndCreateConfig($params['template_id'], $params);
            
            if ($result['code'] === 200) {
                $this->_success('配置创建成功');
            } else {
                $this->_error($result['message']);
            }
        }

        $this->_error('请求方式错误');
    }

    /**
     * 编辑配置
     */
    public function edit()
    {
        $id = $this->request->param('id');
        $config = PosterTemplateConfig::find($id);
        
        if (!$config) {
            $this->_error('配置不存在');
        }

        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 允许更新的字段
            $allowFields = ['config_name', 'config_description', 'parameters'];
            foreach ($allowFields as $field) {
                if (isset($params[$field])) {
                    $config->$field = $params[$field];
                }
            }

            if ($config->save()) {
                $this->_success('更新成功');
            } else {
                $this->_error('更新失败');
            }
        }

        $this->assign('config', $config);
        return $this->fetch();
    }

    /**
     * 删除配置
     */
    public function del()
    {
        $id = $this->request->post('id');
        $config = PosterTemplateConfig::find($id);
        
        if (!$config) {
            $this->_error('配置不存在');
        }

        if ($config->delete()) {
            $this->_success('删除成功');
        } else {
            $this->_error('删除失败');
        }
    }

    /**
     * 修改状态
     */
    public function status()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        $config = PosterTemplateConfig::find($id);
        if (!$config) {
            $this->_error('配置不存在');
        }

        $config->status = $status;
        if ($config->save()) {
            $this->_success('状态修改成功');
        } else {
            $this->_error('状态修改失败');
        }
    }

    /**
     * 解析模板接口
     */
    public function parseTemplate()
    {
        $templateId = $this->request->post('template_id');
        
        if (empty($templateId)) {
            $this->_error('模板ID不能为空');
        }

        $result = $this->posterServer->parseTemplateAndCreateConfig($templateId, []);
        
        if ($result['code'] === 200) {
            $this->_success('解析成功', $result['data']);
        } else {
            $this->_error($result['message']);
        }
    }

    /**
     * 预览配置表单
     */
    public function previewForm()
    {
        $configId = $this->request->param('config_id');
        $config = PosterTemplateConfig::find($configId);
        
        if (!$config) {
            $this->_error('配置不存在');
        }

        $this->assign('config', $config);
        return $this->fetch();
    }

    /**
     * 复制配置
     */
    public function copy()
    {
        $id = $this->request->post('id');
        $config = PosterTemplateConfig::find($id);
        
        if (!$config) {
            $this->_error('配置不存在');
        }

        $newConfig = $config->toArray();
        unset($newConfig['id']);
        $newConfig['config_name'] = $newConfig['config_name'] . '_副本';
        $newConfig['created_by'] = $this->admin_id;

        $result = PosterTemplateConfig::createConfig($newConfig);
        
        if ($result) {
            $this->_success('复制成功');
        } else {
            $this->_error('复制失败');
        }
    }

    /**
     * 导出配置
     */
    public function export()
    {
        $id = $this->request->param('id');
        $config = PosterTemplateConfig::find($id);
        
        if (!$config) {
            $this->_error('配置不存在');
        }

        $exportData = [
            'template_id' => $config->template_id,
            'template_title' => $config->template_title,
            'config_name' => $config->config_name,
            'config_description' => $config->config_description,
            'parameters' => $config->parameters,
            'export_time' => date('Y-m-d H:i:s'),
        ];

        $filename = 'poster_config_' . $config->id . '_' . date('YmdHis') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename=' . $filename);
        echo json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * 导入配置
     */
    public function import()
    {
        if ($this->request->isPost()) {
            $file = $this->request->file('config_file');
            
            if (!$file) {
                $this->_error('请选择配置文件');
            }

            $content = file_get_contents($file->getPathname());
            $configData = json_decode($content, true);
            
            if (!$configData || !isset($configData['parameters'])) {
                $this->_error('配置文件格式错误');
            }

            $configData['created_by'] = $this->admin_id;
            $configData['config_name'] = $configData['config_name'] . '_导入';
            
            $result = PosterTemplateConfig::createConfig($configData);
            
            if ($result) {
                $this->_success('导入成功');
            } else {
                $this->_error('导入失败');
            }
        }

        return $this->fetch();
    }
}
