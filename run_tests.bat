@echo off
chcp 65001 >nul
echo ================================================================================
echo 动态参数模板系统API测试执行脚本
echo ================================================================================
echo.

echo 🚀 开始执行API测试...
echo.

REM 检查curl是否可用
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: curl 未安装或不在PATH中
    echo 请安装curl或确保其在系统PATH中
    pause
    exit /b 1
)

REM 设置变量
set BASE_URL=http://www.likeshop-server.com
set API_KEY=poster_api_key_2025_secure_token_12345
set REPORT_FILE=tests\api_test_report_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt

echo 测试环境: %BASE_URL%
echo API密钥: %API_KEY%
echo 报告文件: %REPORT_FILE%
echo.

REM 创建报告文件
echo ================================================================================ > %REPORT_FILE%
echo 动态参数模板系统API测试报告 >> %REPORT_FILE%
echo ================================================================================ >> %REPORT_FILE%
echo 测试时间: %date% %time% >> %REPORT_FILE%
echo 测试环境: %BASE_URL% >> %REPORT_FILE%
echo API密钥: %API_KEY% >> %REPORT_FILE%
echo ================================================================================ >> %REPORT_FILE%
echo. >> %REPORT_FILE%

REM 测试计数器
set /a TOTAL_TESTS=0
set /a PASSED_TESTS=0
set /a FAILED_TESTS=0

REM 测试函数
goto :start_tests

:run_test
set TEST_NAME=%~1
set METHOD=%~2
set ENDPOINT=%~3
set EXPECTED_CODE=%~4
set HEADERS=%~5

set /a TOTAL_TESTS+=1
echo 🔍 测试: %TEST_NAME%
echo    方法: %METHOD%
echo    端点: %ENDPOINT%

REM 构建curl命令
set CURL_CMD=curl -s -w "%%{http_code}|%%{time_total}" -X %METHOD%

if not "%HEADERS%"=="" (
    set CURL_CMD=%CURL_CMD% -H "%HEADERS%"
)

set CURL_CMD=%CURL_CMD% -H "Content-Type: application/json" "%BASE_URL%%ENDPOINT%"

REM 执行请求
for /f "tokens=1,2 delims=|" %%a in ('%CURL_CMD%') do (
    set HTTP_CODE=%%a
    set TIME_TOTAL=%%b
)

echo    响应码: %HTTP_CODE%
echo    响应时间: %TIME_TOTAL%s

REM 记录到报告
echo 测试: %TEST_NAME% >> %REPORT_FILE%
echo    方法: %METHOD% >> %REPORT_FILE%
echo    端点: %ENDPOINT% >> %REPORT_FILE%
echo    期望码: %EXPECTED_CODE% >> %REPORT_FILE%
echo    实际码: %HTTP_CODE% >> %REPORT_FILE%
echo    响应时间: %TIME_TOTAL%s >> %REPORT_FILE%

REM 验证结果
if "%HTTP_CODE%"=="%EXPECTED_CODE%" (
    echo    结果: ✅ 通过
    echo    结果: 通过 >> %REPORT_FILE%
    set /a PASSED_TESTS+=1
) else (
    echo    结果: ❌ 失败 ^(期望: %EXPECTED_CODE%, 实际: %HTTP_CODE%^)
    echo    结果: 失败 ^(期望: %EXPECTED_CODE%, 实际: %HTTP_CODE%^) >> %REPORT_FILE%
    set /a FAILED_TESTS+=1
)

echo. >> %REPORT_FILE%
echo.
goto :eof

:start_tests

echo ================================================================================ 
echo 外部API测试 ^(供迅排设计服务调用^)
echo ================================================================================

REM 外部健康检查
call :run_test "外部健康检查" "GET" "/api/external/health" "200" "Authorization: Bearer %API_KEY%"

REM 无效API Key测试
call :run_test "无效API Key认证" "GET" "/api/external/health" "401" "Authorization: Bearer invalid_key"

REM 缺少Authorization头测试
call :run_test "缺少Authorization头" "GET" "/api/external/health" "401" ""

echo ================================================================================ 
echo 内部API测试 ^(主项目管理功能^)
echo ================================================================================

REM 模板解析
echo 🔍 测试: 模板解析
echo    方法: POST
echo    端点: /api/poster/parse-template
set /a TOTAL_TESTS+=1

curl -s -w "%%{http_code}|%%{time_total}" -X POST -H "Content-Type: application/json" -d "{\"template_id\": \"2\"}" "%BASE_URL%/api/poster/parse-template" > temp_response.txt 2>&1

for /f "tokens=1,2 delims=|" %%a in (temp_response.txt) do (
    set HTTP_CODE=%%a
    set TIME_TOTAL=%%b
)

echo    响应码: %HTTP_CODE%
echo    响应时间: %TIME_TOTAL%s

echo 测试: 模板解析 >> %REPORT_FILE%
echo    方法: POST >> %REPORT_FILE%
echo    端点: /api/poster/parse-template >> %REPORT_FILE%
echo    期望码: 200 >> %REPORT_FILE%
echo    实际码: %HTTP_CODE% >> %REPORT_FILE%
echo    响应时间: %TIME_TOTAL%s >> %REPORT_FILE%

if "%HTTP_CODE%"=="200" (
    echo    结果: ✅ 通过
    echo    结果: 通过 >> %REPORT_FILE%
    set /a PASSED_TESTS+=1
) else (
    echo    结果: ❌ 失败 ^(期望: 200, 实际: %HTTP_CODE%^)
    echo    结果: 失败 ^(期望: 200, 实际: %HTTP_CODE%^) >> %REPORT_FILE%
    set /a FAILED_TESTS+=1
)

del temp_response.txt 2>nul
echo. >> %REPORT_FILE%
echo.

REM 获取配置列表
call :run_test "获取配置列表" "GET" "/api/poster/config-list?page=1&limit=10" "200" ""

REM 错误处理测试
echo ================================================================================ 
echo 错误处理测试
echo ================================================================================

call :run_test "无效配置ID" "GET" "/api/poster/config-detail/invalid_id" "404" ""
call :run_test "无效用户数据ID" "GET" "/api/poster/user-data-detail/invalid_id" "404" ""

REM 生成测试报告
echo ================================================================================ 
echo 测试结果汇总
echo ================================================================================

echo 总测试数: %TOTAL_TESTS%
echo 通过测试: %PASSED_TESTS%
echo 失败测试: %FAILED_TESTS%

REM 计算成功率
set /a SUCCESS_RATE=%PASSED_TESTS% * 100 / %TOTAL_TESTS%
echo 成功率: %SUCCESS_RATE%%%

REM 写入报告
echo ================================================================================ >> %REPORT_FILE%
echo 测试结果汇总 >> %REPORT_FILE%
echo ================================================================================ >> %REPORT_FILE%
echo 总测试数: %TOTAL_TESTS% >> %REPORT_FILE%
echo 通过测试: %PASSED_TESTS% >> %REPORT_FILE%
echo 失败测试: %FAILED_TESTS% >> %REPORT_FILE%
echo 成功率: %SUCCESS_RATE%%% >> %REPORT_FILE%
echo. >> %REPORT_FILE%

if %FAILED_TESTS% equ 0 (
    echo.
    echo 🎉 所有测试通过！系统API功能正常。
    echo.
    echo 建议下一步操作:
    echo 1. 部署到生产环境
    echo 2. 与迅排设计服务进行联调测试
    echo 3. 进行性能压力测试
    echo 4. 完善监控和日志系统
    
    echo 🎉 所有测试通过！系统API功能正常。 >> %REPORT_FILE%
    echo. >> %REPORT_FILE%
    echo 建议下一步操作: >> %REPORT_FILE%
    echo 1. 部署到生产环境 >> %REPORT_FILE%
    echo 2. 与迅排设计服务进行联调测试 >> %REPORT_FILE%
    echo 3. 进行性能压力测试 >> %REPORT_FILE%
    echo 4. 完善监控和日志系统 >> %REPORT_FILE%
) else (
    echo.
    echo ⚠️  发现 %FAILED_TESTS% 个失败的测试，请检查系统配置。
    echo.
    echo 排查建议:
    echo 1. 检查Web服务器配置和运行状态
    echo 2. 验证数据库连接和表结构
    echo 3. 确认API路由配置正确
    echo 4. 检查PHP错误日志
    
    echo ⚠️  发现 %FAILED_TESTS% 个失败的测试，请检查系统配置。 >> %REPORT_FILE%
    echo. >> %REPORT_FILE%
    echo 排查建议: >> %REPORT_FILE%
    echo 1. 检查Web服务器配置和运行状态 >> %REPORT_FILE%
    echo 2. 验证数据库连接和表结构 >> %REPORT_FILE%
    echo 3. 确认API路由配置正确 >> %REPORT_FILE%
    echo 4. 检查PHP错误日志 >> %REPORT_FILE%
)

echo.
echo ================================================================================ 
echo 测试完成时间: %date% %time%
echo 测试报告保存至: %REPORT_FILE%
echo ================================================================================

echo 测试完成时间: %date% %time% >> %REPORT_FILE%
echo ================================================================================ >> %REPORT_FILE%

echo.
echo 📖 查看详细报告: type %REPORT_FILE%
echo 🌐 Web测试界面: %BASE_URL%/tests/web_api_test.php
echo.

pause
