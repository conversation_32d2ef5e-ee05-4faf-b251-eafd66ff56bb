-- 添加海报管理菜单到后台管理系统
-- 执行此脚本将在后台管理系统中添加海报管理相关菜单

-- 1. 添加海报管理主菜单
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(0, 1, '海报管理', 'layui-icon-template-1', 50, '', 0, UNIX_TIMESTAMP());

-- 获取刚插入的主菜单ID（需要根据实际情况调整）
SET @poster_main_menu_id = LAST_INSERT_ID();

-- 2. 添加配置管理子菜单
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@poster_main_menu_id, 1, '配置管理', 'layui-icon-set', 100, 'poster_manage/config_list', 0, UNIX_TIMESTAMP());

-- 3. 添加数据管理子菜单
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@poster_main_menu_id, 1, '数据管理', 'layui-icon-table', 90, 'poster_manage/data_list', 0, UNIX_TIMESTAMP());

-- 4. 添加统计分析子菜单
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@poster_main_menu_id, 1, '统计分析', 'layui-icon-chart', 80, 'poster_manage/statistics', 0, UNIX_TIMESTAMP());

-- 获取配置管理菜单ID
SET @config_menu_id = (SELECT id FROM `ls_dev_auth` WHERE `name` = '配置管理' AND `pid` = @poster_main_menu_id);

-- 5. 添加配置管理的操作权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@config_menu_id, 2, '查看配置列表', '', 100, 'poster_manage/config_list', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '添加配置', '', 90, 'poster_manage/add_config', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '编辑配置', '', 80, 'poster_manage/edit_config', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '删除配置', '', 70, 'poster_manage/del_config', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '配置详情', '', 60, 'poster_manage/config_detail', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '切换状态', '', 50, 'poster_manage/switch_status', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '复制配置', '', 40, 'poster_manage/copy_config', 0, UNIX_TIMESTAMP()),
(@config_menu_id, 2, '解析模板', '', 30, 'poster_manage/parse_template', 0, UNIX_TIMESTAMP());

-- 获取数据管理菜单ID
SET @data_menu_id = (SELECT id FROM `ls_dev_auth` WHERE `name` = '数据管理' AND `pid` = @poster_main_menu_id);

-- 6. 添加数据管理的操作权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@data_menu_id, 2, '查看数据列表', '', 100, 'poster_manage/data_list', 0, UNIX_TIMESTAMP()),
(@data_menu_id, 2, '数据详情', '', 90, 'poster_manage/data_detail', 0, UNIX_TIMESTAMP()),
(@data_menu_id, 2, '删除数据', '', 80, 'poster_manage/del_data', 0, UNIX_TIMESTAMP()),
(@data_menu_id, 2, '批量删除', '', 70, 'poster_manage/batch_del_data', 0, UNIX_TIMESTAMP()),
(@data_menu_id, 2, '导出数据', '', 60, 'poster_manage/export_data', 0, UNIX_TIMESTAMP());

-- 获取统计分析菜单ID
SET @stats_menu_id = (SELECT id FROM `ls_dev_auth` WHERE `name` = '统计分析' AND `pid` = @poster_main_menu_id);

-- 7. 添加统计分析的操作权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `name`, `icon`, `sort`, `uri`, `disable`, `create_time`) VALUES
(@stats_menu_id, 2, '查看统计', '', 100, 'poster_manage/statistics', 0, UNIX_TIMESTAMP());

-- 8. 为超级管理员角色添加所有海报管理权限（假设超级管理员角色ID为1）
-- 注意：这里需要根据实际的角色表结构进行调整

-- 查看当前系统是否有角色权限表
-- 如果有 ls_role_auth 表，则添加权限关联
-- INSERT INTO `ls_role_auth` (`role_id`, `auth_id`) 
-- SELECT 1, id FROM `ls_dev_auth` WHERE `name` LIKE '%海报%' OR `pid` = @poster_main_menu_id;

-- 9. 添加路由配置（如果需要）
-- 这部分可能需要在 route/route.php 中手动添加

-- 显示添加的菜单信息
SELECT 
    id,
    pid,
    type,
    name,
    icon,
    uri,
    sort,
    FROM_UNIXTIME(create_time) as create_time
FROM `ls_dev_auth` 
WHERE name = '海报管理' 
   OR pid = @poster_main_menu_id 
   OR pid IN (SELECT id FROM `ls_dev_auth` WHERE pid = @poster_main_menu_id)
ORDER BY pid, sort DESC;
