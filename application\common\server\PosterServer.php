<?php

namespace app\common\server;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;
use think\facade\Log;
use think\facade\Config;

/**
 * 海报服务类
 * Class PosterServer
 * @package app\common\server
 */
class PosterServer
{
    private $xunpaiApi;

    public function __construct()
    {
        $this->xunpaiApi = new XunpaiApiServer();
    }

    /**
     * 解析模板并创建配置
     * @param string $templateId
     * @param array $configData
     * @return array
     */
    public function parseTemplateAndCreateConfig($templateId, $configData)
    {
        try {
            // 1. 调用迅排设计API解析模板
            $parseResult = $this->xunpaiApi->parseTemplate($templateId);
            
            if (!$parseResult || !isset($parseResult['parameterCandidates'])) {
                return $this->error('模板解析失败');
            }

            // 2. 处理参数候选项
            $parameterCandidates = $parseResult['parameterCandidates'];
            $templateTitle = $parseResult['templateTitle'] ?? '';

            // 3. 如果提供了配置数据，创建配置
            if (!empty($configData)) {
                $configId = $this->createTemplateConfig($templateId, $templateTitle, $configData, $parameterCandidates);
                if (!$configId) {
                    return $this->error('配置创建失败');
                }

                return $this->success([
                    'config_id' => $configId,
                    'template_title' => $templateTitle,
                    'parameter_candidates' => $parameterCandidates,
                ]);
            }

            // 4. 只返回解析结果
            return $this->success([
                'template_id' => $templateId,
                'template_title' => $templateTitle,
                'parameter_candidates' => $parameterCandidates,
            ]);

        } catch (\Exception $e) {
            Log::error('解析模板失败: ' . $e->getMessage());
            return $this->error('解析模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建模板配置
     * @param string $templateId
     * @param string $templateTitle
     * @param array $configData
     * @param array $parameterCandidates
     * @return string|false
     */
    public function createTemplateConfig($templateId, $templateTitle, $configData, $parameterCandidates = [])
    {
        try {
            // 处理参数配置
            $parameters = [];
            if (!empty($configData['selected_parameters'])) {
                foreach ($configData['selected_parameters'] as $paramId) {
                    $candidate = $this->findParameterCandidate($parameterCandidates, $paramId);
                    if ($candidate) {
                        $parameters[] = [
                            'id' => $candidate['elementUuid'],
                            'elementUuid' => $candidate['elementUuid'],
                            'parameterName' => $candidate['suggestedName'],
                            'parameterLabel' => $configData['parameter_labels'][$paramId] ?? $candidate['suggestedLabel'],
                            'parameterType' => $candidate['suggestedType'],
                            'isRequired' => $configData['parameter_required'][$paramId] ?? false,
                            'defaultValue' => $candidate['originalText'],
                            'isEnabled' => true,
                            'displayOrder' => count($parameters) + 1,
                            'validationRules' => $this->getDefaultValidationRules($candidate['suggestedType']),
                        ];
                    }
                }
            }

            $data = [
                'template_id' => $templateId,
                'template_title' => $templateTitle,
                'config_name' => $configData['config_name'],
                'config_description' => $configData['config_description'] ?? '',
                'parameters' => $parameters,
                'created_by' => $configData['created_by'] ?? 0,
            ];

            $config = PosterTemplateConfig::createConfig($data);
            return $config ? $config->id : false;

        } catch (\Exception $e) {
            Log::error('创建模板配置失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 创建用户数据
     * @param array $data
     * @return array
     */
    public function createUserData($data)
    {
        try {
            // 1. 获取配置信息
            $config = PosterTemplateConfig::find($data['config_id']);
            if (!$config) {
                return $this->error('配置不存在');
            }

            // 2. 验证参数值
            list($isValid, $errors) = PosterUserData::validateParameterValues(
                $data['parameter_values'], 
                $config->parameters
            );

            if (!$isValid) {
                return $this->error('参数验证失败', $errors);
            }

            // 3. 创建用户数据
            $userData = PosterUserData::createData([
                'config_id' => $data['config_id'],
                'template_id' => $config->template_id,
                'user_id' => $data['user_id'] ?? 0,
                'session_id' => $data['session_id'] ?? '',
                'parameter_values' => $data['parameter_values'],
                'is_draft' => $data['is_draft'] ?? PosterUserData::DRAFT_YES,
            ]);

            if (!$userData) {
                return $this->error('数据保存失败');
            }

            return $this->success([
                'data_id' => $userData->id,
                'config_id' => $userData->config_id,
                'template_id' => $userData->template_id,
            ]);

        } catch (\Exception $e) {
            Log::error('创建用户数据失败: ' . $e->getMessage());
            return $this->error('创建用户数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成预览
     * @param string $dataId
     * @return array
     */
    public function generatePreview($dataId)
    {
        try {
            // 1. 获取用户数据
            $userData = PosterUserData::with(['config'])->find($dataId);
            if (!$userData) {
                return $this->error('数据不存在');
            }

            // 2. 调用迅排设计API生成预览
            $previewResult = $this->xunpaiApi->generatePreview($userData->template_id, $dataId);
            
            if (!$previewResult || !isset($previewResult['previewUrl'])) {
                return $this->error('预览生成失败');
            }

            // 3. 更新预览URL
            $previewUrl = $previewResult['previewUrl'];
            PosterUserData::updateData($dataId, ['preview_url' => $previewUrl]);

            return $this->success([
                'preview_url' => $previewUrl,
                'data_id' => $dataId,
            ]);

        } catch (\Exception $e) {
            Log::error('生成预览失败: ' . $e->getMessage());
            return $this->error('生成预览失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成图片
     * @param string $dataId
     * @param array $options
     * @return array
     */
    public function generateImage($dataId, $options = [])
    {
        try {
            $startTime = microtime(true);

            // 1. 获取用户数据
            $userData = PosterUserData::find($dataId);
            if (!$userData) {
                return $this->error('数据不存在');
            }

            // 2. 调用迅排设计API生成图片
            $imageUrl = $this->xunpaiApi->generateImage($dataId, $options);
            
            if (!$imageUrl) {
                return $this->error('图片生成失败');
            }

            $endTime = microtime(true);
            $generationTime = round($endTime - $startTime, 3);

            // 3. 更新用户数据
            PosterUserData::updateData($dataId, [
                'generated_image_url' => $imageUrl,
                'is_draft' => PosterUserData::DRAFT_NO,
            ]);

            // 4. 记录生成记录
            PosterGenerationRecord::createRecord([
                'data_id' => $dataId,
                'image_url' => $imageUrl,
                'generation_options' => $options,
                'generation_time' => $generationTime,
            ]);

            return $this->success([
                'image_url' => $imageUrl,
                'data_id' => $dataId,
                'generation_time' => $generationTime,
            ]);

        } catch (\Exception $e) {
            Log::error('生成图片失败: ' . $e->getMessage());
            return $this->error('生成图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 查找参数候选项
     * @param array $candidates
     * @param string $paramId
     * @return array|null
     */
    private function findParameterCandidate($candidates, $paramId)
    {
        foreach ($candidates as $candidate) {
            if ($candidate['elementUuid'] === $paramId) {
                return $candidate;
            }
        }
        return null;
    }

    /**
     * 获取默认验证规则
     * @param string $type
     * @return array
     */
    private function getDefaultValidationRules($type)
    {
        $rules = Config::get('poster.parameter_types.' . $type . '.validation', []);
        
        $defaultRules = [];
        foreach ($rules as $rule) {
            if (strpos($rule, 'max:') === 0) {
                $defaultRules['maxLength'] = (int)substr($rule, 4);
            } elseif (strpos($rule, 'min:') === 0) {
                $defaultRules['minLength'] = (int)substr($rule, 4);
            }
        }
        
        return $defaultRules;
    }

    /**
     * 成功响应
     * @param mixed $data
     * @param string $message
     * @return array
     */
    private function success($data = [], $message = 'success')
    {
        return [
            'code' => 200,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 错误响应
     * @param string $message
     * @param mixed $data
     * @return array
     */
    private function error($message, $data = [])
    {
        return [
            'code' => 400,
            'message' => $message,
            'data' => $data,
        ];
    }
}
