<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报生成结果</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .result-container { background: #fff; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }
        .result-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .result-content { padding: 30px; }
        .poster-preview { text-align: center; margin-bottom: 30px; }
        .poster-image { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .placeholder-image { width: 400px; height: 600px; background: #f5f5f5; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; margin: 0 auto; border-radius: 8px; }
        .data-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .parameter-display { margin-top: 20px; }
        .parameter-item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .parameter-item:last-child { border-bottom: none; }
        .action-buttons { text-align: center; margin-top: 30px; }
        .status-indicator { display: inline-block; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .progress-container { margin: 20px 0; }
        .generation-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px; max-height: 200px; overflow-y: auto; }
        .log-item { padding: 5px 0; font-size: 12px; color: #666; }
        .log-time { color: #999; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>海报生成结果</h2>
            <p>您的个性化海报已生成完成</p>
        </div>

        <div class="result-container">
            <div class="result-header">
                <h3 id="resultTitle">海报生成中...</h3>
                <p id="resultSubtitle">请稍候，我们正在为您生成专属海报</p>
                <div class="status-indicator" id="statusIndicator">处理中</div>
            </div>

            <div class="result-content">
                <!-- 进度条 -->
                <div class="progress-container" id="progressContainer">
                    <div class="layui-progress layui-progress-big" lay-showpercent="true">
                        <div class="layui-progress-bar layui-bg-blue" lay-percent="0%" id="progressBar"></div>
                    </div>
                </div>

                <!-- 海报预览 -->
                <div class="poster-preview">
                    <div id="posterContainer">
                        <div class="placeholder-image" id="placeholderImage">
                            <div style="text-align: center; color: #999;">
                                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 48px;"></i>
                                <p style="margin-top: 10px;">海报生成中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据信息 -->
                <div class="data-info">
                    <h4>提交信息</h4>
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md6">
                            <p><strong>数据ID:</strong> <span id="dataId">-</span></p>
                            <p><strong>配置名称:</strong> <span id="configName">-</span></p>
                            <p><strong>模板ID:</strong> <span id="templateId">-</span></p>
                        </div>
                        <div class="layui-col-md6">
                            <p><strong>提交时间:</strong> <span id="submitTime">-</span></p>
                            <p><strong>生成状态:</strong> <span id="generationStatus">-</span></p>
                            <p><strong>预计完成:</strong> <span id="estimatedTime">-</span></p>
                        </div>
                    </div>

                    <div class="parameter-display">
                        <h5>参数详情</h5>
                        <div id="parameterList">
                            <!-- 参数列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 生成日志 -->
                <div class="generation-log" id="generationLog" style="display: none;">
                    <h5>生成日志</h5>
                    <div id="logContent">
                        <!-- 日志内容将在这里动态生成 -->
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="layui-btn layui-btn-primary" onclick="goBack()">
                        <i class="layui-icon layui-icon-return"></i> 返回修改
                    </button>
                    <button class="layui-btn layui-btn-normal" onclick="refreshStatus()" id="refreshBtn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新状态
                    </button>
                    <button class="layui-btn" onclick="downloadPoster()" id="downloadBtn" style="display: none;">
                        <i class="layui-icon layui-icon-download-circle"></i> 下载海报
                    </button>
                    <button class="layui-btn layui-btn-warm" onclick="sharePoster()" id="shareBtn" style="display: none;">
                        <i class="layui-icon layui-icon-share"></i> 分享海报
                    </button>
                    <button class="layui-btn layui-btn-danger" onclick="regeneratePoster()" id="regenerateBtn" style="display: none;">
                        <i class="layui-icon layui-icon-refresh-1"></i> 重新生成
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['layer', 'element'], function(){
            var layer = layui.layer;
            var element = layui.element;

            // 从URL获取数据ID
            var urlParams = new URLSearchParams(window.location.search);
            var dataId = urlParams.get('data_id');

            if (!dataId) {
                layer.msg('缺少数据ID参数', {icon: 2}, function(){
                    window.history.back();
                });
                return;
            }

            // 页面加载时获取数据信息
            loadDataInfo(dataId);
            
            // 开始轮询生成状态
            startStatusPolling(dataId);

            // 加载数据信息
            function loadDataInfo(dataId) {
                document.getElementById('dataId').textContent = dataId;
                
                fetch(`/api/poster/user-data-detail/${dataId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            renderDataInfo(data.data);
                            checkGenerationStatus(dataId);
                        } else {
                            layer.msg(data.msg || '加载数据失败', {icon: 2});
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        layer.msg('网络错误', {icon: 2});
                    });
            }

            // 渲染数据信息
            function renderDataInfo(data) {
                document.getElementById('configName').textContent = data.config_name || '未知配置';
                document.getElementById('templateId').textContent = data.template_id;
                document.getElementById('submitTime').textContent = new Date(data.create_time * 1000).toLocaleString();

                // 渲染参数列表
                var parameterList = document.getElementById('parameterList');
                parameterList.innerHTML = '';

                try {
                    var parameters = typeof data.parameter_values === 'string' ? 
                        JSON.parse(data.parameter_values) : data.parameter_values;
                    
                    Object.keys(parameters).forEach(function(key) {
                        var parameterItem = document.createElement('div');
                        parameterItem.className = 'parameter-item';
                        parameterItem.innerHTML = `
                            <span><strong>${key}:</strong></span>
                            <span>${parameters[key] || '<span style="color: #ccc;">空值</span>'}</span>
                        `;
                        parameterList.appendChild(parameterItem);
                    });
                } catch(e) {
                    parameterList.innerHTML = '<p style="color: #999;">参数数据解析失败</p>';
                }
            }

            // 检查生成状态
            function checkGenerationStatus(dataId) {
                fetch(`/api/poster/generation-status/${dataId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            updateGenerationStatus(data.data);
                        } else {
                            // 如果没有生成记录，创建一个
                            createGenerationRecord(dataId);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        updateGenerationStatus({
                            status: 'pending',
                            progress: 0,
                            message: '正在准备生成...'
                        });
                    });
            }

            // 创建生成记录
            function createGenerationRecord(dataId) {
                fetch('/api/poster/create-generation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        data_id: dataId,
                        generation_type: 'final'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        updateGenerationStatus({
                            status: 'pending',
                            progress: 10,
                            message: '生成任务已创建'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }

            // 更新生成状态
            function updateGenerationStatus(status) {
                var statusMap = {
                    'pending': { text: '等待中', class: 'status-pending', progress: 10 },
                    'processing': { text: '生成中', class: 'status-processing', progress: 50 },
                    'completed': { text: '已完成', class: 'status-completed', progress: 100 },
                    'failed': { text: '生成失败', class: 'status-failed', progress: 0 }
                };

                var currentStatus = statusMap[status.status] || statusMap['pending'];
                
                // 更新状态指示器
                var indicator = document.getElementById('statusIndicator');
                indicator.textContent = currentStatus.text;
                indicator.className = 'status-indicator ' + currentStatus.class;

                // 更新进度条
                var progress = status.progress || currentStatus.progress;
                element.progress('demo', progress + '%');

                // 更新标题和副标题
                if (status.status === 'completed') {
                    document.getElementById('resultTitle').textContent = '海报生成完成！';
                    document.getElementById('resultSubtitle').textContent = '您的专属海报已经准备就绪';
                    document.getElementById('progressContainer').style.display = 'none';
                    
                    // 显示海报图片
                    if (status.result_data && status.result_data.image_url) {
                        showPosterImage(status.result_data.image_url);
                    }
                    
                    // 显示操作按钮
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    document.getElementById('shareBtn').style.display = 'inline-block';
                    document.getElementById('refreshBtn').style.display = 'none';
                    
                } else if (status.status === 'failed') {
                    document.getElementById('resultTitle').textContent = '生成失败';
                    document.getElementById('resultSubtitle').textContent = status.error_message || '生成过程中出现错误，请重试';
                    document.getElementById('regenerateBtn').style.display = 'inline-block';
                    
                } else {
                    document.getElementById('resultTitle').textContent = '海报生成中...';
                    document.getElementById('resultSubtitle').textContent = status.message || '请稍候，我们正在为您生成专属海报';
                }

                // 更新生成状态显示
                document.getElementById('generationStatus').textContent = currentStatus.text;
                
                // 更新预计完成时间
                if (status.estimated_completion) {
                    document.getElementById('estimatedTime').textContent = new Date(status.estimated_completion).toLocaleString();
                } else if (status.status === 'processing') {
                    var estimatedTime = new Date(Date.now() + 60000); // 预计1分钟后完成
                    document.getElementById('estimatedTime').textContent = estimatedTime.toLocaleString();
                }

                // 更新生成日志
                if (status.logs && status.logs.length > 0) {
                    updateGenerationLog(status.logs);
                }
            }

            // 显示海报图片
            function showPosterImage(imageUrl) {
                var posterContainer = document.getElementById('posterContainer');
                posterContainer.innerHTML = `
                    <img src="${imageUrl}" alt="生成的海报" class="poster-image" onclick="previewImage('${imageUrl}')">
                `;
            }

            // 更新生成日志
            function updateGenerationLog(logs) {
                var logContainer = document.getElementById('logContent');
                var generationLog = document.getElementById('generationLog');
                
                logContainer.innerHTML = '';
                logs.forEach(function(log) {
                    var logItem = document.createElement('div');
                    logItem.className = 'log-item';
                    logItem.innerHTML = `
                        <span class="log-time">${new Date(log.timestamp).toLocaleTimeString()}</span>
                        <span>${log.message}</span>
                    `;
                    logContainer.appendChild(logItem);
                });
                
                generationLog.style.display = 'block';
                
                // 滚动到最新日志
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            // 开始状态轮询
            var pollingInterval;
            function startStatusPolling(dataId) {
                pollingInterval = setInterval(function() {
                    checkGenerationStatus(dataId);
                }, 3000); // 每3秒检查一次状态
            }

            // 停止状态轮询
            function stopStatusPolling() {
                if (pollingInterval) {
                    clearInterval(pollingInterval);
                }
            }

            // 返回修改
            window.goBack = function() {
                var urlParams = new URLSearchParams(window.location.search);
                var dataId = urlParams.get('data_id');
                
                // 获取配置ID然后跳转
                fetch(`/api/poster/user-data-detail/${dataId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            window.location.href = `/user/poster_data_submit.html?config_id=${data.data.config_id}&data_id=${dataId}`;
                        } else {
                            window.history.back();
                        }
                    })
                    .catch(error => {
                        window.history.back();
                    });
            };

            // 刷新状态
            window.refreshStatus = function() {
                var urlParams = new URLSearchParams(window.location.search);
                var dataId = urlParams.get('data_id');
                checkGenerationStatus(dataId);
                layer.msg('状态已刷新', {icon: 1});
            };

            // 下载海报
            window.downloadPoster = function() {
                var posterImage = document.querySelector('.poster-image');
                if (posterImage) {
                    var link = document.createElement('a');
                    link.href = posterImage.src;
                    link.download = `poster_${dataId}.jpg`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    layer.msg('下载开始', {icon: 1});
                } else {
                    layer.msg('海报图片不可用', {icon: 2});
                }
            };

            // 分享海报
            window.sharePoster = function() {
                var shareUrl = window.location.href;
                
                // 复制链接到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(shareUrl).then(function() {
                        layer.msg('分享链接已复制到剪贴板', {icon: 1});
                    });
                } else {
                    // 降级方案
                    var textArea = document.createElement('textarea');
                    textArea.value = shareUrl;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    layer.msg('分享链接已复制到剪贴板', {icon: 1});
                }
            };

            // 重新生成
            window.regeneratePoster = function() {
                layer.confirm('确定要重新生成海报吗？', {icon: 3, title:'确认重新生成'}, function(index){
                    var urlParams = new URLSearchParams(window.location.search);
                    var dataId = urlParams.get('data_id');
                    
                    fetch('/api/poster/regenerate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            data_id: dataId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            layer.msg('重新生成任务已提交', {icon: 1});
                            layer.close(index);
                            
                            // 重置页面状态
                            document.getElementById('resultTitle').textContent = '海报生成中...';
                            document.getElementById('resultSubtitle').textContent = '请稍候，我们正在为您重新生成专属海报';
                            document.getElementById('progressContainer').style.display = 'block';
                            document.getElementById('posterContainer').innerHTML = `
                                <div class="placeholder-image">
                                    <div style="text-align: center; color: #999;">
                                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 48px;"></i>
                                        <p style="margin-top: 10px;">海报生成中...</p>
                                    </div>
                                </div>
                            `;
                            
                            // 隐藏完成状态的按钮
                            document.getElementById('downloadBtn').style.display = 'none';
                            document.getElementById('shareBtn').style.display = 'none';
                            document.getElementById('regenerateBtn').style.display = 'none';
                            document.getElementById('refreshBtn').style.display = 'inline-block';
                            
                            // 重新开始轮询
                            startStatusPolling(dataId);
                        } else {
                            layer.msg(data.msg || '重新生成失败', {icon: 2});
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        layer.msg('网络错误', {icon: 2});
                    });
                });
            };

            // 预览图片
            window.previewImage = function(imageUrl) {
                layer.photos({
                    photos: {
                        "title": "海报预览",
                        "data": [{
                            "alt": "生成的海报",
                            "pid": 1,
                            "src": imageUrl,
                            "thumb": imageUrl
                        }]
                    }
                });
            };

            // 页面卸载时停止轮询
            window.addEventListener('beforeunload', function() {
                stopStatusPolling();
            });
        });
    </script>
</body>
</html>
