<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户数据管理</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        .container { padding: 20px; }
        .header { margin-bottom: 20px; }
        .filter-panel { background: #fff; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .layui-table-cell { height: auto !important; }
        .status-draft { color: #FFB800; }
        .status-submitted { color: #5FB878; }
        .data-detail { padding: 20px; }
        .parameter-table { margin-top: 15px; }
        .json-viewer { background: #f5f5f5; padding: 15px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>用户数据管理</h2>
            <p>管理和查看用户提交的海报参数数据</p>
        </div>

        <!-- 筛选面板 -->
        <div class="filter-panel">
            <form class="layui-form" id="filterForm">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <label class="layui-form-label">配置选择</label>
                        <div class="layui-input-block">
                            <select name="config_id" id="configSelect" lay-search>
                                <option value="">全部配置</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">数据状态</label>
                        <div class="layui-input-block">
                            <select name="status">
                                <option value="">全部状态</option>
                                <option value="1">草稿</option>
                                <option value="0">已提交</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">用户ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="user_id" placeholder="输入用户ID" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">操作</label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn" onclick="searchData()">
                                <i class="layui-icon layui-icon-search"></i> 查询
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="resetFilter()">
                                <i class="layui-icon layui-icon-refresh"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据列表 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <span>用户数据列表</span>
                <div style="float: right;">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="exportData()">
                        <i class="layui-icon layui-icon-export"></i> 导出数据
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="batchDelete()">
                        <i class="layui-icon layui-icon-delete"></i> 批量删除
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-table" lay-filter="dataTable" id="dataTable"></table>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="layui-row layui-col-space15" style="margin-top: 20px;">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">总数据量</div>
                    <div class="layui-card-body" style="text-align: center; font-size: 24px; color: #1E9FFF;">
                        <span id="totalCount">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">草稿数量</div>
                    <div class="layui-card-body" style="text-align: center; font-size: 24px; color: #FFB800;">
                        <span id="draftCount">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">已提交数量</div>
                    <div class="layui-card-body" style="text-align: center; font-size: 24px; color: #5FB878;">
                        <span id="submittedCount">0</span>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">今日新增</div>
                    <div class="layui-card-body" style="text-align: center; font-size: 24px; color: #FF5722;">
                        <span id="todayCount">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['table', 'form', 'layer', 'laydate'], function(){
            var table = layui.table;
            var form = layui.form;
            var layer = layui.layer;
            var laydate = layui.laydate;

            // 初始化
            loadConfigs();
            loadStatistics();

            // 数据列表表格
            table.render({
                elem: '#dataTable',
                url: '/api/poster/user-data-list',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                cols: [[
                    {type: 'checkbox', fixed: 'left'},
                    {field: 'id', title: '数据ID', width: 200, sort: true},
                    {field: 'config_name', title: '配置名称', width: 150, templet: function(d){
                        return d.config_name || '未知配置';
                    }},
                    {field: 'user_id', title: '用户ID', width: 100, sort: true},
                    {field: 'parameter_count', title: '参数数量', width: 100, templet: function(d){
                        try {
                            var params = JSON.parse(d.parameter_values || '{}');
                            return Object.keys(params).length;
                        } catch(e) {
                            return 0;
                        }
                    }},
                    {field: 'is_draft', title: '状态', width: 100, templet: function(d){
                        return d.is_draft == 1 ? 
                            '<span class="status-draft">草稿</span>' : 
                            '<span class="status-submitted">已提交</span>';
                    }},
                    {field: 'create_time', title: '创建时间', width: 160, sort: true, templet: function(d){
                        return new Date(d.create_time * 1000).toLocaleString();
                    }},
                    {field: 'update_time', title: '更新时间', width: 160, sort: true, templet: function(d){
                        return new Date(d.update_time * 1000).toLocaleString();
                    }},
                    {title: '操作', width: 200, toolbar: '#operationBar', fixed: 'right'}
                ]],
                parseData: function(res){
                    return {
                        "code": res.code === 200 ? 0 : res.code,
                        "msg": res.msg || res.message,
                        "count": res.data ? res.data.total : 0,
                        "data": res.data ? res.data.list : []
                    };
                },
                done: function(res, curr, count) {
                    // 更新统计信息
                    updateStatistics(res.data);
                }
            });

            // 操作栏模板
            var operationBarTpl = `
                <script type="text/html" id="operationBar">
                    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="download">下载</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
                </script>
            `;
            $('body').append(operationBarTpl);

            // 监听工具条
            table.on('tool(dataTable)', function(obj){
                var data = obj.data;
                if(obj.event === 'view'){
                    viewDataDetail(data);
                } else if(obj.event === 'edit'){
                    editData(data);
                } else if(obj.event === 'download'){
                    downloadData(data);
                } else if(obj.event === 'delete'){
                    deleteData(data);
                }
            });

            // 加载配置列表
            function loadConfigs() {
                fetch('/api/poster/config-list?page=1&limit=1000')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200 && data.data.list) {
                            var select = document.getElementById('configSelect');
                            data.data.list.forEach(function(config) {
                                var option = document.createElement('option');
                                option.value = config.id;
                                option.textContent = config.config_name;
                                select.appendChild(option);
                            });
                            form.render('select');
                        }
                    })
                    .catch(error => {
                        console.error('加载配置列表失败:', error);
                    });
            }

            // 加载统计信息
            function loadStatistics() {
                fetch('/api/poster/data-statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            updateStatisticsDisplay(data.data);
                        }
                    })
                    .catch(error => {
                        console.error('加载统计信息失败:', error);
                    });
            }

            // 更新统计显示
            function updateStatisticsDisplay(stats) {
                document.getElementById('totalCount').textContent = stats.total || 0;
                document.getElementById('draftCount').textContent = stats.draft || 0;
                document.getElementById('submittedCount').textContent = stats.submitted || 0;
                document.getElementById('todayCount').textContent = stats.today || 0;
            }

            // 查看数据详情
            window.viewDataDetail = function(data) {
                fetch(`/api/poster/user-data-detail/${data.id}`)
                    .then(response => response.json())
                    .then(result => {
                        if (result.code === 200) {
                            showDataDetailModal(result.data);
                        } else {
                            layer.msg(result.msg || '获取详情失败', {icon: 2});
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        layer.msg('网络错误', {icon: 2});
                    });
            };

            // 显示数据详情模态框
            function showDataDetailModal(data) {
                var parameterValues = {};
                try {
                    parameterValues = typeof data.parameter_values === 'string' ? 
                        JSON.parse(data.parameter_values) : data.parameter_values;
                } catch(e) {
                    parameterValues = {};
                }

                var parameterTableHtml = '<table class="layui-table parameter-table"><thead><tr><th>参数名</th><th>参数值</th></tr></thead><tbody>';
                Object.keys(parameterValues).forEach(function(key) {
                    parameterTableHtml += `<tr><td>${key}</td><td>${parameterValues[key] || '<span style="color: #ccc;">空值</span>'}</td></tr>`;
                });
                parameterTableHtml += '</tbody></table>';

                var content = `
                    <div class="data-detail">
                        <h3>数据详情</h3>
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md6">
                                <p><strong>数据ID:</strong> ${data.id}</p>
                                <p><strong>配置ID:</strong> ${data.config_id}</p>
                                <p><strong>模板ID:</strong> ${data.template_id}</p>
                                <p><strong>用户ID:</strong> ${data.user_id || '未知'}</p>
                            </div>
                            <div class="layui-col-md6">
                                <p><strong>状态:</strong> ${data.is_draft == 1 ? '<span class="status-draft">草稿</span>' : '<span class="status-submitted">已提交</span>'}</p>
                                <p><strong>创建时间:</strong> ${new Date(data.create_time * 1000).toLocaleString()}</p>
                                <p><strong>更新时间:</strong> ${new Date(data.update_time * 1000).toLocaleString()}</p>
                            </div>
                        </div>
                        <h4>参数值:</h4>
                        ${parameterTableHtml}
                        <h4>原始JSON数据:</h4>
                        <div class="json-viewer">${JSON.stringify(parameterValues, null, 2)}</div>
                    </div>
                `;
                
                layer.open({
                    type: 1,
                    title: '数据详情',
                    area: ['800px', '600px'],
                    content: content,
                    btn: ['关闭'],
                    yes: function(index) {
                        layer.close(index);
                    }
                });
            }

            // 编辑数据
            window.editData = function(data) {
                // 跳转到编辑页面
                window.open(`/user/poster_data_submit.html?config_id=${data.config_id}&data_id=${data.id}`, '_blank');
            };

            // 下载数据
            window.downloadData = function(data) {
                var content = {
                    id: data.id,
                    config_id: data.config_id,
                    template_id: data.template_id,
                    user_id: data.user_id,
                    parameter_values: JSON.parse(data.parameter_values || '{}'),
                    is_draft: data.is_draft,
                    create_time: new Date(data.create_time * 1000).toISOString(),
                    update_time: new Date(data.update_time * 1000).toISOString()
                };

                var blob = new Blob([JSON.stringify(content, null, 2)], {type: 'application/json'});
                var url = URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = `poster_data_${data.id}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                layer.msg('下载成功', {icon: 1});
            };

            // 删除数据
            window.deleteData = function(data) {
                layer.confirm('确定要删除这条数据吗？删除后无法恢复！', {icon: 3, title:'确认删除'}, function(index){
                    // 这里应该调用删除API
                    layer.msg('删除功能待实现', {icon: 1});
                    layer.close(index);
                });
            };

            // 搜索数据
            window.searchData = function() {
                var formData = new FormData(document.getElementById('filterForm'));
                var where = {};
                
                for (let [key, value] of formData.entries()) {
                    if (value) {
                        where[key] = value;
                    }
                }

                table.reload('dataTable', {
                    where: where,
                    page: {
                        curr: 1
                    }
                });
            };

            // 重置筛选
            window.resetFilter = function() {
                document.getElementById('filterForm').reset();
                form.render();
                table.reload('dataTable', {
                    where: {},
                    page: {
                        curr: 1
                    }
                });
            };

            // 导出数据
            window.exportData = function() {
                layer.confirm('确定要导出当前筛选的数据吗？', {icon: 3, title:'确认导出'}, function(index){
                    layer.msg('导出功能待实现', {icon: 1});
                    layer.close(index);
                });
            };

            // 批量删除
            window.batchDelete = function() {
                var checkStatus = table.checkStatus('dataTable');
                var data = checkStatus.data;
                
                if (data.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }

                layer.confirm(`确定要删除选中的 ${data.length} 条数据吗？删除后无法恢复！`, {icon: 3, title:'确认批量删除'}, function(index){
                    layer.msg('批量删除功能待实现', {icon: 1});
                    layer.close(index);
                });
            };
        });
    </script>
</body>
</html>
