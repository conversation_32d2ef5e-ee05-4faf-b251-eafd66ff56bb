<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态参数模板系统</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; padding: 40px 20px; }
        .header { text-align: center; color: white; margin-bottom: 50px; }
        .header h1 { font-size: 48px; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 18px; opacity: 0.9; }
        .nav-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-bottom: 50px; }
        .nav-card { background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.2); transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .nav-card:hover { transform: translateY(-5px); box-shadow: 0 15px 40px rgba(0,0,0,0.3); }
        .nav-card .icon { font-size: 48px; margin-bottom: 20px; }
        .nav-card h3 { margin-bottom: 15px; color: #333; }
        .nav-card p { color: #666; margin-bottom: 25px; line-height: 1.6; }
        .nav-card .layui-btn { width: 100%; }
        .admin-card .icon { color: #FF5722; }
        .user-card .icon { color: #2196F3; }
        .doc-card .icon { color: #4CAF50; }
        .api-card .icon { color: #FF9800; }
        .features { background: rgba(255,255,255,0.1); border-radius: 15px; padding: 40px; margin-bottom: 30px; }
        .features h2 { color: white; text-align: center; margin-bottom: 30px; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .feature-item { color: white; text-align: center; }
        .feature-item .layui-icon { font-size: 32px; margin-bottom: 10px; display: block; }
        .footer { text-align: center; color: rgba(255,255,255,0.8); margin-top: 50px; }
        .status-info { background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-bottom: 30px; }
        .status-item { display: inline-block; margin: 0 20px; color: white; text-align: center; }
        .status-value { font-size: 24px; font-weight: bold; display: block; }
        .status-label { font-size: 12px; opacity: 0.8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>动态参数模板系统</h1>
            <p>强大的海报生成工具，支持可配置的模板和参数化生成</p>
        </div>

        <!-- 系统状态 -->
        <div class="status-info">
            <div class="status-item">
                <span class="status-value" id="configCount">-</span>
                <span class="status-label">配置数量</span>
            </div>
            <div class="status-item">
                <span class="status-value" id="dataCount">-</span>
                <span class="status-label">数据总量</span>
            </div>
            <div class="status-item">
                <span class="status-value" id="todayCount">-</span>
                <span class="status-label">今日新增</span>
            </div>
            <div class="status-item">
                <span class="status-value">100%</span>
                <span class="status-label">系统健康</span>
            </div>
        </div>

        <!-- 导航卡片 -->
        <div class="nav-cards">
            <div class="nav-card admin-card">
                <div class="icon">
                    <i class="layui-icon layui-icon-set"></i>
                </div>
                <h3>管理后台</h3>
                <p>管理模板配置、查看用户数据、监控系统状态。提供完整的后台管理功能。</p>
                <button class="layui-btn layui-btn-danger" onclick="goToAdmin()">
                    <i class="layui-icon layui-icon-login"></i> 进入管理后台
                </button>
            </div>

            <div class="nav-card user-card">
                <div class="icon">
                    <i class="layui-icon layui-icon-user"></i>
                </div>
                <h3>用户中心</h3>
                <p>填写海报参数、生成个性化海报、查看历史记录。简单易用的用户界面。</p>
                <button class="layui-btn" onclick="goToUser()">
                    <i class="layui-icon layui-icon-right"></i> 开始使用
                </button>
            </div>

            <div class="nav-card api-card">
                <div class="icon">
                    <i class="layui-icon layui-icon-code"></i>
                </div>
                <h3>API接口</h3>
                <p>完整的API文档和调用示例，支持第三方系统集成。RESTful设计，易于集成。</p>
                <button class="layui-btn layui-btn-warm" onclick="goToApi()">
                    <i class="layui-icon layui-icon-file"></i> 查看文档
                </button>
            </div>

            <div class="nav-card doc-card">
                <div class="icon">
                    <i class="layui-icon layui-icon-read"></i>
                </div>
                <h3>帮助文档</h3>
                <p>详细的使用指南、技术文档、常见问题解答。帮助您快速上手使用系统。</p>
                <button class="layui-btn layui-btn-normal" onclick="goToDoc()">
                    <i class="layui-icon layui-icon-help"></i> 查看文档
                </button>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="features">
            <h2>核心功能特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="layui-icon layui-icon-template"></i>
                    <h4>模板配置</h4>
                    <p>灵活的模板参数配置，支持多种参数类型和验证规则</p>
                </div>
                <div class="feature-item">
                    <i class="layui-icon layui-icon-form"></i>
                    <h4>数据管理</h4>
                    <p>完善的用户数据管理，支持草稿保存和批量操作</p>
                </div>
                <div class="feature-item">
                    <i class="layui-icon layui-icon-api"></i>
                    <h4>API接口</h4>
                    <p>RESTful API设计，支持内外部系统无缝集成</p>
                </div>
                <div class="feature-item">
                    <i class="layui-icon layui-icon-vercode"></i>
                    <h4>安全认证</h4>
                    <p>API密钥认证机制，确保数据安全和访问控制</p>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>&copy; 2025 动态参数模板系统 | 基于 ThinkPHP 5.1 构建 | 版本 v1.0</p>
            <p>系统状态：<span style="color: #4CAF50;">● 正常运行</span> | 最后更新：2025年8月17日</p>
        </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;

            // 页面加载时获取系统状态
            loadSystemStatus();

            // 加载系统状态
            function loadSystemStatus() {
                // 获取配置数量
                fetch('/api/poster/config-list?page=1&limit=1')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            document.getElementById('configCount').textContent = data.data.total || 0;
                        }
                    })
                    .catch(error => {
                        console.error('获取配置数量失败:', error);
                        document.getElementById('configCount').textContent = '0';
                    });

                // 获取数据统计（如果API存在）
                fetch('/api/poster/data-statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            document.getElementById('dataCount').textContent = data.data.total || 0;
                            document.getElementById('todayCount').textContent = data.data.today || 0;
                        }
                    })
                    .catch(error => {
                        console.error('获取数据统计失败:', error);
                        document.getElementById('dataCount').textContent = '0';
                        document.getElementById('todayCount').textContent = '0';
                    });
            }

            // 进入管理后台
            window.goToAdmin = function() {
                // 显示管理后台选项
                layer.open({
                    type: 1,
                    title: '选择管理功能',
                    area: ['400px', '300px'],
                    content: `
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid" onclick="window.open('/admin/poster_config_management.html', '_blank')">
                                    <i class="layui-icon layui-icon-set"></i> 配置管理
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-normal" onclick="window.open('/admin/poster_data_management.html', '_blank')">
                                    <i class="layui-icon layui-icon-table"></i> 数据管理
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-warm" onclick="window.open('/docs/API接口详细文档.md', '_blank')">
                                    <i class="layui-icon layui-icon-chart"></i> 系统监控
                                </button>
                            </div>
                        </div>
                    `
                });
            };

            // 进入用户中心
            window.goToUser = function() {
                // 获取可用配置列表
                fetch('/api/poster/config-list?page=1&limit=10&status=1')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200 && data.data.list.length > 0) {
                            showConfigSelection(data.data.list);
                        } else {
                            layer.msg('暂无可用配置，请先创建配置', {icon: 2});
                        }
                    })
                    .catch(error => {
                        console.error('获取配置列表失败:', error);
                        layer.msg('获取配置列表失败', {icon: 2});
                    });
            };

            // 显示配置选择
            function showConfigSelection(configs) {
                var configOptions = configs.map(config => 
                    `<option value="${config.id}">${config.config_name} (${config.template_title || config.template_id})</option>`
                ).join('');

                layer.open({
                    type: 1,
                    title: '选择模板配置',
                    area: ['500px', '300px'],
                    content: `
                        <div style="padding: 20px;">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择配置:</label>
                                <div class="layui-input-block">
                                    <select id="configSelect">
                                        <option value="">请选择配置</option>
                                        ${configOptions}
                                    </select>
                                </div>
                            </div>
                            <div style="text-align: center; margin-top: 30px;">
                                <button class="layui-btn" onclick="startWithConfig()">
                                    <i class="layui-icon layui-icon-right"></i> 开始填写
                                </button>
                            </div>
                        </div>
                    `,
                    success: function() {
                        layui.form.render('select');
                    }
                });
            }

            // 使用选定配置开始
            window.startWithConfig = function() {
                var configId = document.getElementById('configSelect').value;
                if (!configId) {
                    layer.msg('请选择一个配置', {icon: 2});
                    return;
                }
                
                layer.closeAll();
                window.open(`/user/poster_data_submit.html?config_id=${configId}`, '_blank');
            };

            // 查看API文档
            window.goToApi = function() {
                layer.open({
                    type: 1,
                    title: 'API文档和工具',
                    area: ['500px', '400px'],
                    content: `
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid" onclick="window.open('/docs/API接口详细文档.md', '_blank')">
                                    <i class="layui-icon layui-icon-file"></i> API接口文档
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-normal" onclick="testApiHealth()">
                                    <i class="layui-icon layui-icon-ok"></i> API健康检查
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-warm" onclick="window.open('/tests/postman_collection.json', '_blank')">
                                    <i class="layui-icon layui-icon-download-circle"></i> Postman集合
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-danger" onclick="runApiTest()">
                                    <i class="layui-icon layui-icon-play"></i> 运行API测试
                                </button>
                            </div>
                        </div>
                    `
                });
            };

            // 查看帮助文档
            window.goToDoc = function() {
                layer.open({
                    type: 1,
                    title: '帮助文档',
                    area: ['500px', '400px'],
                    content: `
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid" onclick="window.open('/docs/动态参数模板功能使用指南.md', '_blank')">
                                    <i class="layui-icon layui-icon-read"></i> 功能使用指南
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-normal" onclick="window.open('/docs/API功能测试报告.md', '_blank')">
                                    <i class="layui-icon layui-icon-chart"></i> 测试报告
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-warm" onclick="window.open('/docs/项目完成状态总结.md', '_blank')">
                                    <i class="layui-icon layui-icon-file"></i> 项目状态
                                </button>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <button class="layui-btn layui-btn-fluid layui-btn-danger" onclick="window.open('/tests/测试工具使用说明.md', '_blank')">
                                    <i class="layui-icon layui-icon-util"></i> 测试工具说明
                                </button>
                            </div>
                        </div>
                    `
                });
            };

            // 测试API健康状态
            window.testApiHealth = function() {
                var loadingIndex = layer.load(2, {content: '检查中...'});
                
                fetch('/api/external/health', {
                    headers: {
                        'Authorization': 'Bearer poster_api_key_2025_secure_token_12345'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    layer.close(loadingIndex);
                    if (data.code === 200) {
                        layer.msg('API服务正常', {icon: 1});
                    } else {
                        layer.msg('API服务异常: ' + data.msg, {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadingIndex);
                    layer.msg('API服务不可用', {icon: 2});
                });
            };

            // 运行API测试
            window.runApiTest = function() {
                layer.confirm('确定要运行完整的API测试吗？这可能需要几分钟时间。', {icon: 3, title:'确认测试'}, function(index){
                    layer.close(index);
                    layer.msg('API测试功能需要在服务器端运行测试脚本', {icon: 1});
                });
            };
        });
    </script>
</body>
</html>
