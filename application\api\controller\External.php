<?php

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterApiKey;
use think\Controller;
use think\facade\Request;
use think\facade\Log;
use think\facade\Cache;

/**
 * 外部API控制器 - 供迅排设计服务调用
 * Class External
 * @package app\api\controller
 */
class External extends Controller
{
    /**
     * 前置操作 - API认证
     */
    public function initialize()
    {
        parent::initialize();
        
        // API认证
        if (!$this->authenticateApi()) {
            $this->apiError('API认证失败', 401);
        }
        
        // 记录API调用日志
        $this->logApiCall();
    }

    /**
     * 获取参数数据
     * GET /api/external/parameter-data/:dataId
     * @param string $dataId
     */
    public function getParameterData($dataId)
    {
        try {
            // 参数验证
            if (empty($dataId)) {
                $this->apiError('参数数据ID不能为空', 400);
            }

            // 缓存检查
            $cacheKey = "poster_external_data_{$dataId}";
            if (Cache::has($cacheKey)) {
                $data = Cache::get($cacheKey);
                $this->apiSuccess($data);
            }

            // 查询数据
            $userData = PosterUserData::with(['config'])->find($dataId);
            if (!$userData) {
                $this->apiError('参数数据不存在', 404);
            }

            // 构造响应数据
            $responseData = [
                'id' => $userData->id,
                'configId' => $userData->config_id,
                'templateId' => $userData->template_id,
                'parameterValues' => $userData->parameter_values,
            ];

            // 缓存数据
            Cache::set($cacheKey, $responseData, 1800); // 30分钟缓存

            $this->apiSuccess($responseData);

        } catch (\Exception $e) {
            Log::error('获取参数数据失败: ' . $e->getMessage(), [
                'data_id' => $dataId,
                'trace' => $e->getTraceAsString(),
            ]);
            $this->apiError('获取参数数据失败', 500);
        }
    }

    /**
     * 获取参数配置
     * GET /api/external/parameter-config/:configId
     * @param string $configId
     */
    public function getParameterConfig($configId)
    {
        try {
            // 参数验证
            if (empty($configId)) {
                $this->apiError('配置ID不能为空', 400);
            }

            // 缓存检查
            $cacheKey = "poster_external_config_{$configId}";
            if (Cache::has($cacheKey)) {
                $data = Cache::get($cacheKey);
                $this->apiSuccess($data);
            }

            // 查询配置
            $config = PosterTemplateConfig::find($configId);
            if (!$config || $config->status != PosterTemplateConfig::STATUS_ENABLED) {
                $this->apiError('参数配置不存在或已禁用', 404);
            }

            // 构造响应数据
            $responseData = [
                'id' => $config->id,
                'templateId' => $config->template_id,
                'parameters' => $config->parameters,
            ];

            // 缓存配置（较长时间）
            Cache::set($cacheKey, $responseData, 3600); // 1小时缓存

            $this->apiSuccess($responseData);

        } catch (\Exception $e) {
            Log::error('获取参数配置失败: ' . $e->getMessage(), [
                'config_id' => $configId,
                'trace' => $e->getTraceAsString(),
            ]);
            $this->apiError('获取参数配置失败', 500);
        }
    }

    /**
     * 健康检查
     * GET /api/external/health
     */
    public function health()
    {
        try {
            // 检查数据库连接
            $dbStatus = $this->checkDatabaseConnection();
            
            // 检查缓存连接
            $cacheStatus = $this->checkCacheConnection();

            $responseData = [
                'status' => 'ok',
                'timestamp' => date('c'),
                'database' => $dbStatus ? 'connected' : 'disconnected',
                'cache' => $cacheStatus ? 'connected' : 'disconnected',
                'version' => '1.0.0',
            ];

            $this->apiSuccess($responseData);

        } catch (\Exception $e) {
            Log::error('健康检查失败: ' . $e->getMessage());
            $this->apiError('健康检查失败', 500);
        }
    }

    /**
     * API认证
     * @return bool
     */
    private function authenticateApi()
    {
        $authorization = Request::header('Authorization');
        if (empty($authorization)) {
            return false;
        }

        // 解析Bearer Token
        if (strpos($authorization, 'Bearer ') !== 0) {
            return false;
        }

        $apiKey = substr($authorization, 7);
        if (empty($apiKey)) {
            return false;
        }

        // 验证API Key
        return $this->validateApiKey($apiKey);
    }

    /**
     * 验证API Key
     * @param string $apiKey
     * @return bool
     */
    private function validateApiKey($apiKey)
    {
        try {
            // 从缓存中获取
            $cacheKey = "poster_api_key_{$apiKey}";
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // 从数据库验证
            $keyRecord = db('poster_api_keys')
                ->where('api_key', $apiKey)
                ->where('is_active', 1)
                ->where('delete_time', null)
                ->find();

            $isValid = !empty($keyRecord);

            // 缓存结果
            Cache::set($cacheKey, $isValid, 300); // 5分钟缓存

            // 更新最后使用时间
            if ($isValid) {
                db('poster_api_keys')
                    ->where('api_key', $apiKey)
                    ->update(['last_used_time' => time()]);
            }

            return $isValid;

        } catch (\Exception $e) {
            Log::error('API Key验证失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 记录API调用日志
     */
    private function logApiCall()
    {
        $logData = [
            'method' => Request::method(),
            'url' => Request::url(true),
            'ip' => Request::ip(),
            'user_agent' => Request::header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s'),
        ];

        Log::info('外部API调用', $logData);
    }

    /**
     * 检查数据库连接
     * @return bool
     */
    private function checkDatabaseConnection()
    {
        try {
            db()->query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查缓存连接
     * @return bool
     */
    private function checkCacheConnection()
    {
        try {
            Cache::set('health_check', 'ok', 10);
            $result = Cache::get('health_check');
            Cache::rm('health_check');
            return $result === 'ok';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * API成功响应
     * @param mixed $data
     * @param string $message
     */
    private function apiSuccess($data = [], $message = 'success')
    {
        $response = [
            'code' => 200,
            'data' => $data,
        ];

        if ($message !== 'success') {
            $response['message'] = $message;
        }

        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * API错误响应
     * @param string $message
     * @param int $code
     * @param mixed $data
     */
    private function apiError($message, $code = 400, $data = [])
    {
        $response = [
            'code' => $code,
            'message' => $message,
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        header('Content-Type: application/json; charset=utf-8');
        http_response_code($code);
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
