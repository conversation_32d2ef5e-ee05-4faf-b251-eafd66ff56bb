<?php

namespace app\common\server;

use think\facade\Cache;
use think\facade\Log;
use think\facade\Config;

/**
 * 迅排设计API服务类
 * Class XunpaiApiServer
 * @package app\common\server
 */
class XunpaiApiServer
{
    private $baseUrl;
    private $timeout;
    private $retryAttempts;
    private $cacheEnabled;
    private $cacheTtl;

    public function __construct()
    {
        $config = Config::get('poster.xunpai_service');
        $this->baseUrl = $config['base_url'];
        $this->timeout = $config['api_timeout'];
        $this->retryAttempts = $config['retry_attempts'];
        $this->cacheEnabled = $config['cache_enabled'];
        $this->cacheTtl = $config['cache_ttl'];
    }

    /**
     * 解析模板内容
     * @param string $templateId
     * @return array
     * @throws \Exception
     */
    public function parseTemplate($templateId)
    {
        $cacheKey = "poster_template_parse_{$templateId}";
        
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/api/template/parse';
        $data = ['templateId' => $templateId];
        
        $result = $this->makeRequest('POST', $url, $data);
        
        if ($this->cacheEnabled && $result) {
            Cache::set($cacheKey, $result, $this->cacheTtl);
        }
        
        return $result;
    }

    /**
     * 生成参数化预览
     * @param string $templateId
     * @param string $parameterDataId
     * @return array
     * @throws \Exception
     */
    public function generatePreview($templateId, $parameterDataId)
    {
        $url = $this->baseUrl . '/api/parameter/preview';
        $data = [
            'templateId' => $templateId,
            'parameterDataId' => $parameterDataId
        ];
        
        return $this->makeRequest('POST', $url, $data);
    }

    /**
     * 生成参数化图片
     * @param string $parameterDataId
     * @param array $options
     * @return string 图片URL
     * @throws \Exception
     */
    public function generateImage($parameterDataId, $options = [])
    {
        $params = [
            'parameterDataId' => $parameterDataId,
            'width' => $options['width'] ?? 800,
            'height' => $options['height'] ?? 600,
            'type' => $options['type'] ?? 'file',
            'size' => $options['size'] ?? 2,
            'quality' => $options['quality'] ?? 0.9,
        ];
        
        $url = $this->baseUrl . '/api/screenshots?' . http_build_query($params);
        
        // 图片生成使用GET请求，返回图片URL
        $response = $this->makeHttpRequest('GET', $url);
        
        if ($response['code'] === 200) {
            return $response['data']['imageUrl'] ?? $url;
        }
        
        throw new \Exception('图片生成失败: ' . ($response['message'] ?? '未知错误'));
    }

    /**
     * 批量生成图片
     * @param array $dataIds
     * @param array $outputOptions
     * @return array
     * @throws \Exception
     */
    public function batchGenerate($dataIds, $outputOptions = [])
    {
        $url = $this->baseUrl . '/api/parameter/batch-generate';
        $data = [
            'dataIds' => $dataIds,
            'outputOptions' => array_merge([
                'width' => 800,
                'height' => 600,
                'type' => 'file',
                'size' => 2,
                'quality' => 0.9,
            ], $outputOptions)
        ];
        
        return $this->makeRequest('POST', $url, $data);
    }

    /**
     * 获取批量生成状态
     * @param string $batchId
     * @return array
     * @throws \Exception
     */
    public function getBatchStatus($batchId)
    {
        $url = $this->baseUrl . "/api/parameter/batch-status/{$batchId}";
        return $this->makeRequest('GET', $url);
    }

    /**
     * 获取模板列表
     * @param array $params 查询参数
     * @return array
     * @throws \Exception
     */
    public function getTemplateList($params = [])
    {
        $defaultParams = [
            'search' => '',
            'page' => 1,
            'pageSize' => 20,
            'cate' => '',
            'type' => 0
        ];

        $queryParams = array_merge($defaultParams, $params);

        // 构建缓存键
        $cacheKey = 'poster_template_list_' . md5(serialize($queryParams));

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/api/external/templates?' . http_build_query($queryParams);

        try {
            $result = $this->makeHttpRequest('GET', $url);

            if ($result['code'] === 200) {
                $data = $result['result'];

                // 缓存结果
                if ($this->cacheEnabled) {
                    Cache::set($cacheKey, $data, $this->cacheTtl);
                }

                return $data;
            } else {
                throw new \Exception($result['msg'] ?? '获取模板列表失败');
            }
        } catch (\Exception $e) {
            \app\common\server\PosterErrorHandler::logApiError('GET', $url, $queryParams, $e->getMessage(), [
                'operation' => 'getTemplateList'
            ]);

            $userError = \app\common\server\PosterErrorHandler::formatUserError(\app\common\server\PosterErrorHandler::ERROR_TYPE_API, $e->getMessage());
            throw new \Exception($userError);
        }
    }

    /**
     * 获取模板详情
     * @param string $templateId
     * @return array
     * @throws \Exception
     */
    public function getTemplateDetail($templateId)
    {
        $cacheKey = "poster_template_detail_{$templateId}";

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . "/api/external/templates/{$templateId}";

        try {
            $result = $this->makeHttpRequest('GET', $url);

            if ($result['code'] === 200) {
                $data = $result['result'];

                // 缓存结果
                if ($this->cacheEnabled) {
                    Cache::set($cacheKey, $data, $this->cacheTtl);
                }

                return $data;
            } else {
                throw new \Exception($result['msg'] ?? '获取模板详情失败');
            }
        } catch (\Exception $e) {
            \app\common\server\PosterErrorHandler::logApiError('GET', $url, ['template_id' => $templateId], $e->getMessage(), [
                'operation' => 'getTemplateDetail'
            ]);

            $userError = \app\common\server\PosterErrorHandler::formatUserError(\app\common\server\PosterErrorHandler::ERROR_TYPE_API, $e->getMessage());
            throw new \Exception($userError);
        }
    }

    /**
     * 搜索模板
     * @param string $keyword 搜索关键词
     * @param array $filters 过滤条件
     * @return array
     * @throws \Exception
     */
    public function searchTemplates($keyword, $filters = [])
    {
        $params = array_merge([
            'search' => $keyword,
            'page' => 1,
            'pageSize' => 20,
            'type' => 0
        ], $filters);

        return $this->getTemplateList($params);
    }

    /**
     * 获取模板分类列表
     * @return array
     * @throws \Exception
     */
    public function getTemplateCategories()
    {
        $cacheKey = 'poster_template_categories';

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/api/external/categories';

        try {
            $result = $this->makeHttpRequest('GET', $url);

            if ($result['code'] === 200) {
                $data = $result['result'];

                // 缓存结果（分类变化较少，缓存时间更长）
                if ($this->cacheEnabled) {
                    Cache::set($cacheKey, $data, $this->cacheTtl * 24); // 24小时
                }

                return $data;
            } else {
                throw new \Exception($result['msg'] ?? '获取模板分类失败');
            }
        } catch (\Exception $e) {
            \app\common\server\PosterErrorHandler::logApiError('GET', $url, [], $e->getMessage(), [
                'operation' => 'getTemplateCategories'
            ]);

            $userError = \app\common\server\PosterErrorHandler::formatUserError(\app\common\server\PosterErrorHandler::ERROR_TYPE_API, $e->getMessage());
            throw new \Exception($userError);
        }
    }

    /**
     * 健康检查
     * @return bool
     */
    public function healthCheck()
    {
        try {
            $url = $this->baseUrl . '/api/health';
            $result = $this->makeRequest('GET', $url);
            return isset($result['status']) && $result['status'] === 'ok';
        } catch (\Exception $e) {
            Log::error('迅排设计服务健康检查失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送HTTP请求
     * @param string $method
     * @param string $url
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function makeRequest($method, $url, $data = [])
    {
        $response = $this->makeHttpRequest($method, $url, $data);
        
        if (!isset($response['code'])) {
            throw new \Exception('迅排设计服务响应格式错误');
        }
        
        if ($response['code'] !== 200) {
            throw new \Exception('迅排设计服务错误: ' . ($response['message'] ?? '未知错误'));
        }
        
        return $response['data'] ?? $response;
    }

    /**
     * 执行HTTP请求（带重试机制）
     * @param string $method
     * @param string $url
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function makeHttpRequest($method, $url, $data = [])
    {
        $lastError = null;
        
        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $ch = curl_init();

                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => $this->timeout,
                    CURLOPT_CUSTOMREQUEST => $method,
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/json',
                        'Accept: application/json',
                    ],
                ]);

                if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                if ($error) {
                    throw new \Exception("CURL错误: {$error}");
                }

                if ($httpCode >= 400) {
                    throw new \Exception("HTTP错误: {$httpCode}");
                }

                $result = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('响应JSON解析失败: ' . json_last_error_msg());
                }

                // 记录成功日志
                Log::info("迅排设计API调用成功: {$method} {$url}", [
                    'attempt' => $attempt,
                    'response_code' => $httpCode,
                ]);

                return $result;
                
            } catch (\Exception $e) {
                $lastError = $e;
                
                Log::warning("迅排设计API调用失败 (尝试 {$attempt}/{$this->retryAttempts}): {$e->getMessage()}", [
                    'method' => $method,
                    'url' => $url,
                    'data' => $data,
                ]);
                
                if ($attempt < $this->retryAttempts) {
                    // 指数退避重试
                    $delay = pow(2, $attempt - 1);
                    sleep($delay);
                }
            }
        }
        
        throw new \Exception("迅排设计API调用失败，已重试 {$this->retryAttempts} 次: " . $lastError->getMessage());
    }
}
