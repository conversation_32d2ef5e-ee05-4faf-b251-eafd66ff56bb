<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\logic\PosterManageLogic;
use app\common\model\PosterTemplateConfig;
use app\common\server\XunpaiApiServer;
use app\common\server\PosterErrorHandler;
use app\common\model\PosterUserData;

/**
 * 海报管理控制器
 * Class PosterManage
 * @package app\admin\controller
 */
class PosterManage extends AdminBase
{
    /**
     * 配置管理页面
     * @return mixed
     */
    public function configList()
    {
        if ($this->request->isAjax()) {
            try {
                $get = $this->request->get();
                $result = PosterManageLogic::getConfigLists($get);

                // 返回 Layui 表格期望的格式
                return json([
                    'code' => 0,
                    'msg' => '获取成功',
                    'count' => $result['count'],
                    'data' => $result['lists']
                ]);
            } catch (\think\exception\HttpResponseException $e) {
                // HttpResponseException 是正常的响应异常，需要重新抛出
                throw $e;
            } catch (\Exception $e) {
                // 记录错误日志
                \think\facade\Log::error('配置列表获取失败: ' . $e->getMessage(), [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
                return $this->error('获取配置列表失败: ' . $e->getMessage());
            }
        }
        return $this->fetch();
    }

    /**
     * 添加配置
     * @return mixed
     */
    public function addConfig()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = PosterManageLogic::addConfig($post);
            if ($result) {
                return $this->success('添加成功');
            }
            return $this->error(PosterManageLogic::getError());
        }
        return $this->fetch();
    }

    /**
     * 编辑配置
     * @return mixed
     */
    public function editConfig()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            $result = PosterManageLogic::editConfig($post);
            if ($result) {
                return $this->success('编辑成功');
            }
            return $this->error(PosterManageLogic::getError());
        }

        $id = $this->request->get('id');
        $detail = PosterTemplateConfig::find($id);
        if (!$detail) {
            $this->error('配置不存在');
        }

        $this->assign('detail', $detail);
        return $this->fetch();
    }

    /**
     * 删除配置
     * @return mixed
     */
    public function delConfig()
    {
        $id = $this->request->post('id');
        $result = PosterManageLogic::delConfig($id);
        if ($result) {
            return $this->success('删除成功');
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 配置详情
     * @return mixed
     */
    public function configDetail()
    {
        $id = $this->request->get('id');
        $detail = PosterTemplateConfig::find($id);
        if (!$detail) {
            $this->error('配置不存在');
        }

        // 解析参数
        try {
            if (is_array($detail->parameters)) {
                $parameters = $detail->parameters;
            } elseif (is_string($detail->parameters)) {
                $parameters = json_decode($detail->parameters, true);
            } else {
                $parameters = [];
            }
        } catch (\Exception $e) {
            $parameters = [];
        }
        $detail->parameters_array = $parameters ?: [];

        $this->assign('detail', $detail);
        return $this->fetch();
    }

    /**
     * 模板选择页面
     * @return mixed
     */
    public function templateSelect()
    {
        if ($this->request->isAjax()) {
            try {
                $get = $this->request->get();
                $xunpaiApi = new XunpaiApiServer();

                $params = [
                    'search' => $get['search'] ?? '',
                    'page' => $get['page'] ?? 1,
                    'pageSize' => $get['limit'] ?? 20,
                    'cate' => $get['cate'] ?? '',
                    'type' => $get['type'] ?? 0
                ];

                $result = $xunpaiApi->getTemplateList($params);

                return $this->success('获取成功', '', [
                    'count' => $result['pagination']['total'] ?? 0,
                    'lists' => $result['list'] ?? [],
                    'page_no' => $result['pagination']['page'] ?? 1,
                    'page_size' => $result['pagination']['pageSize'] ?? 20
                ]);
            } catch (\think\exception\HttpResponseException $e) {
                // HttpResponseException 是正常的响应异常，需要重新抛出
                throw $e;
            } catch (\Exception $e) {
                PosterErrorHandler::logSystemError('templateSelect', $e, $get ?? []);
                return $this->error($e->getMessage());
            }
        }

        // 获取模板分类
        try {
            $xunpaiApi = new XunpaiApiServer();
            $categories = $xunpaiApi->getTemplateCategories();
            $this->assign('categories', $categories);
        } catch (\Exception $e) {
            $this->assign('categories', []);
        }

        return $this->fetch();
    }

    /**
     * 获取模板详情
     * @return mixed
     */
    public function getTemplateDetail()
    {
        $templateId = $this->request->get('template_id');
        if (empty($templateId)) {
            return $this->error('模板ID不能为空');
        }

        try {
            $xunpaiApi = new XunpaiApiServer();
            $detail = $xunpaiApi->getTemplateDetail($templateId);
            return $this->success('获取成功', '', $detail);
        } catch (\Exception $e) {
            PosterErrorHandler::logSystemError('getTemplateDetail', $e, ['template_id' => $templateId]);
            return $this->error($e->getMessage());
        }
    }



    /**
     * 用户数据列表
     * @return mixed
     */
    public function dataList()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = PosterManageLogic::getDataLists($get);

            // 返回 Layui 表格期望的格式
            return json([
                'code' => 0,
                'msg' => '获取成功',
                'count' => $result['count'],
                'data' => $result['lists']
            ]);
        }

        // 获取配置列表用于筛选
        $configs = PosterTemplateConfig::where('status', 1)
            ->field('id,config_name,template_title')
            ->select();
        $this->assign('configs', $configs);

        return $this->fetch();
    }

    /**
     * 用户数据详情
     * @return mixed
     */
    public function dataDetail()
    {
        $id = $this->request->get('id');
        $detail = PosterUserData::find($id);
        if (!$detail) {
            $this->error('数据不存在');
        }

        // 获取配置信息
        $config = PosterTemplateConfig::find($detail->config_id);
        $detail->config_info = $config;

        // 解析参数值
        $parameterValues = json_decode($detail->parameter_values, true);
        $detail->parameter_values_array = $parameterValues ?: [];

        $this->assign('detail', $detail);
        return $this->fetch();
    }

    /**
     * 删除用户数据
     * @return mixed
     */
    public function delData()
    {
        $id = $this->request->post('id');
        $result = PosterManageLogic::delData($id);
        if ($result) {
            return $this->success('删除成功');
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 批量删除用户数据
     * @return mixed
     */
    public function batchDelData()
    {
        $ids = $this->request->post('ids');
        if (empty($ids)) {
            return $this->error('请选择要删除的数据');
        }

        $result = PosterManageLogic::batchDelData($ids);
        if ($result) {
            return $this->success('批量删除成功');
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 导出数据
     * @return mixed
     */
    public function exportData()
    {
        $get = $this->request->get();
        $result = PosterManageLogic::exportData($get);
        if ($result) {
            return $this->success('导出成功', '', $result);
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 统计数据
     * @return mixed
     */
    public function statistics()
    {
        if ($this->request->isAjax()) {
            $data = PosterManageLogic::getStatistics();
            return $this->success('获取成功', '', $data);
        }
        return $this->fetch();
    }

    /**
     * 模板解析
     * @return mixed
     */
    public function parseTemplate()
    {
        $templateId = $this->request->post('template_id');
        if (empty($templateId)) {
            return $this->error('模板ID不能为空');
        }

        try {
            $xunpaiApi = new XunpaiApiServer();
            $result = $xunpaiApi->parseTemplate($templateId);
            return $this->success('解析成功', '', $result);
        } catch (\Exception $e) {
            PosterErrorHandler::logSystemError('parseTemplate', $e, ['template_id' => $templateId]);
            return $this->error($e->getMessage());
        }
    }

    /**
     * 切换配置状态
     * @return mixed
     */
    public function switchStatus()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');
        
        $result = PosterManageLogic::switchStatus($id, $status);
        if ($result) {
            return $this->success('状态切换成功');
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 复制配置
     * @return mixed
     */
    public function copyConfig()
    {
        $id = $this->request->post('id');
        $result = PosterManageLogic::copyConfig($id);
        if ($result) {
            return $this->success('复制成功', '', $result);
        }
        return $this->error(PosterManageLogic::getError());
    }

    /**
     * 数据库检查和初始化
     * @return mixed
     */
    public function checkDatabase()
    {
        try {
            $tableName = 'ls_poster_template_configs';

            // 检查表是否存在
            $sql = "SHOW TABLES LIKE '{$tableName}'";
            $result = \think\facade\Db::query($sql);

            if (empty($result)) {
                // 创建表
                $createSql = "
CREATE TABLE `{$tableName}` (
  `id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` varchar(255) DEFAULT '' COMMENT '模板标题',
  `config_name` varchar(255) NOT NULL COMMENT '配置名称',
  `config_description` text COMMENT '配置描述',
  `parameters` json NOT NULL COMMENT '参数定义JSON',
  `created_by` int(11) DEFAULT 0 COMMENT '创建者ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报模板参数配置表';
";

                \think\facade\Db::execute($createSql);

                // 创建用户数据表
                $userDataTable = 'ls_poster_user_data';
                $createUserDataSql = "
CREATE TABLE `{$userDataTable}` (
  `id` varchar(32) NOT NULL COMMENT '数据ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `user_id` int(11) DEFAULT 0 COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT '' COMMENT '会话ID（匿名用户）',
  `parameter_values` json NOT NULL COMMENT '用户填写的参数值',
  `is_draft` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为草稿：1草稿 0正式',
  `preview_url` varchar(500) DEFAULT '' COMMENT '预览页面URL',
  `generated_image_url` varchar(500) DEFAULT '' COMMENT '生成的图片URL',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_is_draft` (`is_draft`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='海报用户参数数据表';
";

                \think\facade\Db::execute($createUserDataSql);

                // 插入一些测试数据
                $testConfig = [
                    'id' => 'test_config_' . time(),
                    'template_id' => 'test_template_001',
                    'template_title' => '测试模板',
                    'config_name' => '测试配置',
                    'config_description' => '这是一个测试配置',
                    'parameters' => json_encode([
                        [
                            'id' => 'param1',
                            'elementUuid' => 'uuid1',
                            'parameterName' => 'title',
                            'parameterLabel' => '标题',
                            'parameterType' => 'text',
                            'isRequired' => true,
                            'isEnabled' => true,
                            'displayOrder' => 1
                        ]
                    ]),
                    'created_by' => 1,
                    'create_time' => time(),
                    'update_time' => time(),
                    'status' => 1
                ];

                \think\facade\Db::table($tableName)->insert($testConfig);

                return $this->success('数据库表创建成功，并插入了测试数据');
            } else {
                return $this->success('数据库表已存在');
            }
        } catch (\Exception $e) {
            return $this->error('数据库检查失败: ' . $e->getMessage());
        }
    }

    /**
     * 清理缓存
     * @return mixed
     */
    public function clearCache()
    {
        try {
            // 清理 OpCache
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }

            // 清理应用缓存
            \think\facade\Cache::clear();

            return $this->success('缓存清理成功');
        } catch (\Exception $e) {
            return $this->error('缓存清理失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试API连接
     * @return mixed
     */
    public function testApi()
    {
        return $this->success('简单测试成功', '', [
            'message' => '这是一个简单的测试接口',
            'timestamp' => time(),
            'request_method' => $this->request->method(),
            'is_ajax' => $this->request->isAjax()
        ]);
    }

    /**
     * 测试表格数据
     * @return mixed
     */
    public function testTable()
    {
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => 2,
            'data' => [
                [
                    'id' => 'test_001',
                    'config_name' => '测试配置1',
                    'template_title' => '测试模板1',
                    'template_id' => '1',
                    'parameter_count' => 3,
                    'status' => 1,
                    'create_time_text' => '2025-08-17 16:00:00',
                    'update_time_text' => '2025-08-17 16:00:00'
                ],
                [
                    'id' => 'test_002',
                    'config_name' => '测试配置2',
                    'template_title' => '测试模板2',
                    'template_id' => '2',
                    'parameter_count' => 5,
                    'status' => 0,
                    'create_time_text' => '2025-08-17 15:00:00',
                    'update_time_text' => '2025-08-17 15:00:00'
                ]
            ]
        ]);
    }

    /**
     * 测试真实API连接
     * @return mixed
     */
    public function testRealApi()
    {
        try {
            // 直接测试API连接，不使用复杂的逻辑
            $url = 'http://localhost:7001/api/external/templates?page=1&pageSize=5';

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Accept: application/json',
                ],
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                return $this->error("CURL错误: {$error}");
            }

            if ($httpCode !== 200) {
                return $this->error("HTTP错误: {$httpCode}");
            }

            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->error('JSON解析失败: ' . json_last_error_msg());
            }

            return $this->success('API测试成功', '', [
                'http_code' => $httpCode,
                'response' => $result
            ]);

        } catch (\Exception $e) {
            return $this->error('API测试失败: ' . $e->getMessage());
        }
    }
}
