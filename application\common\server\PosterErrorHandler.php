<?php

namespace app\common\server;

use think\facade\Log;
use think\facade\Config;

/**
 * 海报系统错误处理类
 * Class PosterErrorHandler
 * @package app\common\server
 */
class PosterErrorHandler
{
    /**
     * 错误类型常量
     */
    const ERROR_TYPE_API = 'api_error';
    const ERROR_TYPE_VALIDATION = 'validation_error';
    const ERROR_TYPE_SYSTEM = 'system_error';
    const ERROR_TYPE_NETWORK = 'network_error';
    const ERROR_TYPE_TIMEOUT = 'timeout_error';

    /**
     * 错误级别常量
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 记录API调用错误
     * @param string $method HTTP方法
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @param string $error 错误信息
     * @param array $context 上下文信息
     */
    public static function logApiError($method, $url, $params, $error, $context = [])
    {
        $logData = [
            'type' => self::ERROR_TYPE_API,
            'method' => $method,
            'url' => $url,
            'params' => $params,
            'error' => $error,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => self::getClientIp(),
        ];

        Log::error('海报系统API调用失败', $logData);
        
        // 如果是生产环境，发送告警通知
        if (Config::get('app.app_debug') === false) {
            self::sendAlert('API调用失败', $error, $logData);
        }
    }

    /**
     * 记录验证错误
     * @param string $field 字段名
     * @param string $value 字段值
     * @param string $rule 验证规则
     * @param string $message 错误信息
     */
    public static function logValidationError($field, $value, $rule, $message)
    {
        $logData = [
            'type' => self::ERROR_TYPE_VALIDATION,
            'field' => $field,
            'value' => $value,
            'rule' => $rule,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getClientIp(),
        ];

        Log::warning('海报系统验证失败', $logData);
    }

    /**
     * 记录系统错误
     * @param string $operation 操作名称
     * @param \Exception $exception 异常对象
     * @param array $context 上下文信息
     */
    public static function logSystemError($operation, $exception, $context = [])
    {
        $logData = [
            'type' => self::ERROR_TYPE_SYSTEM,
            'operation' => $operation,
            'error' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getClientIp(),
        ];

        Log::error('海报系统错误', $logData);
        
        // 如果是生产环境，发送告警通知
        if (Config::get('app.app_debug') === false) {
            self::sendAlert('系统错误', $exception->getMessage(), $logData);
        }
    }

    /**
     * 记录网络错误
     * @param string $url 请求URL
     * @param string $error 错误信息
     * @param int $httpCode HTTP状态码
     * @param array $context 上下文信息
     */
    public static function logNetworkError($url, $error, $httpCode = 0, $context = [])
    {
        $logData = [
            'type' => self::ERROR_TYPE_NETWORK,
            'url' => $url,
            'error' => $error,
            'http_code' => $httpCode,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getClientIp(),
        ];

        Log::error('海报系统网络错误', $logData);
    }

    /**
     * 记录超时错误
     * @param string $operation 操作名称
     * @param int $timeout 超时时间
     * @param array $context 上下文信息
     */
    public static function logTimeoutError($operation, $timeout, $context = [])
    {
        $logData = [
            'type' => self::ERROR_TYPE_TIMEOUT,
            'operation' => $operation,
            'timeout' => $timeout,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getClientIp(),
        ];

        Log::warning('海报系统超时', $logData);
    }

    /**
     * 记录成功操作
     * @param string $operation 操作名称
     * @param array $data 操作数据
     * @param array $context 上下文信息
     */
    public static function logSuccess($operation, $data = [], $context = [])
    {
        $logData = [
            'operation' => $operation,
            'data' => $data,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => self::getClientIp(),
        ];

        Log::info('海报系统操作成功', $logData);
    }

    /**
     * 格式化用户友好的错误信息
     * @param string $errorType 错误类型
     * @param string $originalError 原始错误信息
     * @return string
     */
    public static function formatUserError($errorType, $originalError)
    {
        $errorMessages = [
            self::ERROR_TYPE_API => '模板服务暂时不可用，请稍后重试',
            self::ERROR_TYPE_VALIDATION => '输入的数据格式不正确，请检查后重试',
            self::ERROR_TYPE_SYSTEM => '系统出现异常，请联系管理员',
            self::ERROR_TYPE_NETWORK => '网络连接异常，请检查网络后重试',
            self::ERROR_TYPE_TIMEOUT => '操作超时，请稍后重试',
        ];

        $userMessage = $errorMessages[$errorType] ?? '操作失败，请稍后重试';
        
        // 在开发环境下显示详细错误信息
        if (Config::get('app.app_debug')) {
            $userMessage .= ' (详细信息: ' . $originalError . ')';
        }

        return $userMessage;
    }

    /**
     * 获取客户端IP地址
     * @return string
     */
    private static function getClientIp()
    {
        $ip = '';
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        return $ip;
    }

    /**
     * 发送告警通知
     * @param string $title 告警标题
     * @param string $message 告警信息
     * @param array $data 详细数据
     */
    private static function sendAlert($title, $message, $data = [])
    {
        // 这里可以集成邮件、短信、钉钉等告警通知
        // 暂时只记录到日志
        Log::critical('海报系统告警', [
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s'),
        ]);
    }

    /**
     * 检查错误频率，防止频繁错误
     * @param string $key 错误键
     * @param int $maxCount 最大错误次数
     * @param int $timeWindow 时间窗口（秒）
     * @return bool 是否超过频率限制
     */
    public static function checkErrorRate($key, $maxCount = 10, $timeWindow = 300)
    {
        $cacheKey = 'poster_error_rate_' . md5($key);
        $cache = cache($cacheKey);
        
        if (!$cache) {
            $cache = ['count' => 0, 'start_time' => time()];
        }
        
        // 如果超过时间窗口，重置计数
        if (time() - $cache['start_time'] > $timeWindow) {
            $cache = ['count' => 0, 'start_time' => time()];
        }
        
        $cache['count']++;
        cache($cacheKey, $cache, $timeWindow);
        
        return $cache['count'] > $maxCount;
    }

    /**
     * 生成错误报告
     * @param int $hours 统计小时数
     * @return array
     */
    public static function generateErrorReport($hours = 24)
    {
        // 这里可以实现错误统计报告功能
        // 暂时返回空数组
        return [
            'total_errors' => 0,
            'error_types' => [],
            'error_trends' => [],
            'top_errors' => [],
        ];
    }
}
