<?php
/**
 * 动态参数模板系统路由配置
 */

use think\facade\Route;

// 外部API路由组 - 供迅排设计服务调用
Route::group('api/external', function () {
    // 获取参数数据
    Route::get('parameter-data/:dataId', 'api/External/getParameterData');
    
    // 获取参数配置
    Route::get('parameter-config/:configId', 'api/External/getParameterConfig');
    
    // 健康检查
    Route::get('health', 'api/External/health');
})->middleware(['cors']);

// 内部API路由组 - 主项目管理使用
Route::group('api/poster', function () {
    // 模板管理
    Route::post('parse-template', 'api/Poster/parseTemplate');
    Route::post('create-config', 'api/Poster/createConfig');
    Route::get('config-list', 'api/Poster/getConfigList');
    Route::get('config-detail/:id', 'api/Poster/getConfigDetail');
    Route::put('update-config/:id', 'api/Poster/updateConfig');
    Route::delete('delete-config/:id', 'api/Poster/deleteConfig');
    
    // 用户数据管理
    Route::post('submit-data', 'api/Poster/submitData');
    Route::get('user-data-list', 'api/Poster/getUserDataList');
    Route::get('user-data-detail/:id', 'api/Poster/getUserDataDetail');
    Route::put('update-user-data/:id', 'api/Poster/updateUserData');
    
    // 预览和生成
    Route::post('generate-preview', 'api/Poster/generatePreview');
    Route::post('generate-image', 'api/Poster/generateImage');
})->middleware(['cors']);

// 管理后台路由组
Route::group('admin/poster', function () {
    // 配置管理
    Route::get('config/index', 'admin/poster/Config/index');
    Route::get('config/add', 'admin/poster/Config/add');
    Route::post('config/add', 'admin/poster/Config/add');
    Route::get('config/edit', 'admin/poster/Config/edit');
    Route::post('config/edit', 'admin/poster/Config/edit');
    Route::post('config/del', 'admin/poster/Config/del');
    Route::post('config/status', 'admin/poster/Config/status');
    
    // 用户数据管理
    Route::get('data/index', 'admin/poster/Data/index');
    Route::get('data/detail', 'admin/poster/Data/detail');
    Route::post('data/del', 'admin/poster/Data/del');
    
    // 生成记录管理
    Route::get('record/index', 'admin/poster/Record/index');
    Route::get('record/statistics', 'admin/poster/Record/statistics');
    
    // API密钥管理
    Route::get('apikey/index', 'admin/poster/ApiKey/index');
    Route::get('apikey/add', 'admin/poster/ApiKey/add');
    Route::post('apikey/add', 'admin/poster/ApiKey/add');
    Route::post('apikey/del', 'admin/poster/ApiKey/del');
    Route::post('apikey/status', 'admin/poster/ApiKey/status');
});

// 前台用户路由组
Route::group('poster', function () {
    // 模板展示
    Route::get('template/list', 'index/poster/Template/lists');
    Route::get('template/detail/:id', 'index/poster/Template/detail');
    
    // 表单填写
    Route::get('form/:configId', 'index/poster/Form/index');
    Route::post('form/submit', 'index/poster/Form/submit');
    Route::get('form/preview/:dataId', 'index/poster/Form/preview');
    
    // 图片生成
    Route::post('generate/:dataId', 'index/poster/Generate/index');
    Route::get('download/:dataId', 'index/poster/Generate/download');
    
    // 用户中心
    Route::get('user/list', 'index/poster/User/lists');
    Route::get('user/detail/:id', 'index/poster/User/detail');
});

// 跨域中间件
Route::group('', function () {
    Route::options('api/external/:path', function () {
        return '';
    });
    Route::options('api/poster/:path', function () {
        return '';
    });
})->pattern(['path' => '.*']);
