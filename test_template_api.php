<?php
/**
 * 模板API功能测试脚本
 * 用于测试新添加的模板选择功能
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use app\common\server\XunpaiApiServer;
use app\common\server\PosterErrorHandler;

echo "=== 海报模板选择功能测试 ===\n\n";

try {
    $xunpaiApi = new XunpaiApiServer();
    
    // 测试1: 健康检查
    echo "1. 测试API健康检查...\n";
    $healthStatus = $xunpaiApi->healthCheck();
    echo "健康检查结果: " . ($healthStatus ? "正常" : "异常") . "\n\n";
    
    // 测试2: 获取模板列表
    echo "2. 测试获取模板列表...\n";
    try {
        $templateList = $xunpaiApi->getTemplateList([
            'page' => 1,
            'pageSize' => 5,
            'search' => ''
        ]);
        
        echo "获取模板列表成功!\n";
        echo "总数: " . ($templateList['pagination']['total'] ?? 0) . "\n";
        echo "当前页: " . ($templateList['pagination']['page'] ?? 0) . "\n";
        echo "模板数量: " . count($templateList['list'] ?? []) . "\n";
        
        if (!empty($templateList['list'])) {
            echo "第一个模板信息:\n";
            $firstTemplate = $templateList['list'][0];
            echo "  ID: " . ($firstTemplate['id'] ?? 'N/A') . "\n";
            echo "  标题: " . ($firstTemplate['title'] ?? 'N/A') . "\n";
            echo "  尺寸: " . ($firstTemplate['width'] ?? 'N/A') . " x " . ($firstTemplate['height'] ?? 'N/A') . "\n";
        }
        
    } catch (\Exception $e) {
        echo "获取模板列表失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试3: 搜索模板
    echo "3. 测试搜索模板...\n";
    try {
        $searchResult = $xunpaiApi->searchTemplates('海报', [
            'page' => 1,
            'pageSize' => 3
        ]);
        
        echo "搜索模板成功!\n";
        echo "搜索结果数量: " . count($searchResult['list'] ?? []) . "\n";
        
    } catch (\Exception $e) {
        echo "搜索模板失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试4: 获取模板分类
    echo "4. 测试获取模板分类...\n";
    try {
        $categories = $xunpaiApi->getTemplateCategories();
        echo "获取模板分类成功!\n";
        echo "分类数量: " . count($categories ?? []) . "\n";
        
    } catch (\Exception $e) {
        echo "获取模板分类失败: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 测试5: 模板解析（如果有可用的模板ID）
    if (!empty($templateList['list'])) {
        echo "5. 测试模板解析...\n";
        $templateId = $templateList['list'][0]['id'] ?? null;
        
        if ($templateId) {
            try {
                $parseResult = $xunpaiApi->parseTemplate($templateId);
                echo "模板解析成功!\n";
                echo "可用参数数量: " . count($parseResult['available_parameters'] ?? []) . "\n";
                
            } catch (\Exception $e) {
                echo "模板解析失败: " . $e->getMessage() . "\n";
            }
        } else {
            echo "没有可用的模板ID进行解析测试\n";
        }
        echo "\n";
    }
    
    echo "=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

// 测试错误处理功能
echo "\n=== 错误处理功能测试 ===\n";

// 测试API错误记录
PosterErrorHandler::logApiError('GET', 'http://test.com/api', ['test' => 'data'], '测试错误信息');
echo "API错误记录测试完成\n";

// 测试验证错误记录
PosterErrorHandler::logValidationError('template_id', 'invalid_id', 'required', '模板ID不能为空');
echo "验证错误记录测试完成\n";

// 测试系统错误记录
try {
    throw new \Exception('测试系统错误');
} catch (\Exception $e) {
    PosterErrorHandler::logSystemError('test_operation', $e, ['test_context' => 'test_value']);
    echo "系统错误记录测试完成\n";
}

// 测试错误频率检查
$isOverLimit = PosterErrorHandler::checkErrorRate('test_key', 5, 300);
echo "错误频率检查测试: " . ($isOverLimit ? "超过限制" : "正常") . "\n";

echo "\n=== 所有测试完成 ===\n";
