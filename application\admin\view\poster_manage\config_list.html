{layout name="layout1" /}
<div class="layui-fluid">
<div class="layui-card">
    <div class="layui-card-header">
        <span>海报模板配置管理</span>
        <div class="layui-btn-group fr">
            <a class="layui-btn layui-btn-sm layui-btn-warm" href="javascript:;" id="selectTemplate">
                <i class="layui-icon layui-icon-template-1"></i> 选择模板
            </a>
            <a class="layui-btn layui-btn-sm layui-btn-normal" href="{:url('add_config')}">
                <i class="layui-icon layui-icon-add-1"></i> 添加配置
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <form class="layui-form" lay-filter="search">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <input type="text" name="keyword" placeholder="配置名称" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <input type="text" name="template_id" placeholder="模板ID" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-col-md2">
                    <select name="status">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="layui-col-md2">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                </div>
                <div class="layui-col-md2">
                    <button type="reset" class="layui-btn layui-btn-primary">
                        <i class="layui-icon layui-icon-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </form>

        <!-- 数据表格 -->
        <table class="layui-hide" id="configTable" lay-filter="configTable"></table>
    </div>
</div>

<!-- 操作栏模板 -->
<script type="text/html" id="operationBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">详情</a>
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="copy">复制</a>
        {{# if(d.status == 1) { }}
        <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="disable">禁用</a>
        {{# } else { }}
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable">启用</a>
        {{# } }}
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
    </div>
</script>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
    {{# if(d.status == 1) { }}
    <span class="layui-badge layui-bg-green">启用</span>
    {{# } else { }}
    <span class="layui-badge">禁用</span>
    {{# } }}
</script>

</div>

<script>
layui.use(['table', 'form', 'layer', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    // 选择模板按钮点击事件
    $('#selectTemplate').click(function(){
        layer.open({
            type: 2,
            title: '选择海报模板',
            content: '{:url("template_select")}',
            area: ['90%', '80%'],
            maxmin: true,
            end: function(){
                // 弹窗关闭后刷新列表
                table.reload('configTable');
            }
        });
    });

    // 监听来自模板选择页面的消息
    window.addEventListener('message', function(event) {
        if (event.data.type === 'templateSelected') {
            var templateData = event.data.data;
            // 跳转到添加配置页面，并传递模板信息
            location.href = '{:url("add_config")}?template_id=' + templateData.template_id +
                           '&template_title=' + encodeURIComponent(templateData.template_title);
        }
    });

    // 渲染表格
    console.log('开始渲染表格...');
    var tableIns = table.render({
        elem: '#configTable',
        url: '{:url("test_table")}',
        page: true,
        limit: 20,
        limits: [10, 20, 50, 100],
        cols: [[
            {field: 'id', title: 'ID', width: 200, sort: true},
            {field: 'config_name', title: '配置名称', width: 200},
            {field: 'template_title', title: '模板标题', width: 150},
            {field: 'template_id', title: '模板ID', width: 100},
            {field: 'parameter_count', title: '参数数量', width: 100, align: 'center'},
            {field: 'status', title: '状态', width: 80, align: 'center', templet: '#statusTpl'},
            {field: 'create_time_text', title: '创建时间', width: 160},
            {field: 'update_time_text', title: '更新时间', width: 160},
            {title: '操作', width: 300, toolbar: '#operationBar', fixed: 'right'}
        ]],
        done: function(res, curr, count){
            console.log('表格渲染完成:', res, curr, count);
        },
        error: function(res, msg){
            console.log('表格渲染错误:', res, msg);
        }
    });
    console.log('表格实例:', tableIns);

    // 监听搜索
    form.on('submit(search)', function(data){
        table.reload('configTable', {
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });

    // 监听工具条
    table.on('tool(configTable)', function(obj){
        var data = obj.data;
        var layEvent = obj.event;

        if(layEvent === 'edit'){
            location.href = '{:url("edit_config")}?id=' + data.id;
        } else if(layEvent === 'detail'){
            location.href = '{:url("config_detail")}?id=' + data.id;
        } else if(layEvent === 'copy'){
            layer.confirm('确定要复制这个配置吗？', {icon: 3, title:'提示'}, function(index){
                $.post('{:url("copy_config")}', {id: data.id}, function(res){
                    if(res.code == 1){
                        layer.msg('复制成功', {icon: 1});
                        table.reload('configTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if(layEvent === 'enable' || layEvent === 'disable'){
            var status = layEvent === 'enable' ? 1 : 0;
            var text = layEvent === 'enable' ? '启用' : '禁用';
            
            layer.confirm('确定要' + text + '这个配置吗？', {icon: 3, title:'提示'}, function(index){
                $.post('{:url("switch_status")}', {id: data.id, status: status}, function(res){
                    if(res.code == 1){
                        layer.msg(text + '成功', {icon: 1});
                        table.reload('configTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        } else if(layEvent === 'delete'){
            layer.confirm('确定要删除这个配置吗？删除后无法恢复！', {icon: 3, title:'提示'}, function(index){
                $.post('{:url("del_config")}', {id: data.id}, function(res){
                    if(res.code == 1){
                        layer.msg('删除成功', {icon: 1});
                        table.reload('configTable');
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        }
    });
});
</script>
